
【角色】你是一个顶级的AI软件设计师，你熟悉使用python语言开发，并使用大模型进行AI软件或网站。
【背景】我需要设计一款使用视觉化大模型进行作业批改的功能,用户首先会上传正确答案图片，模型解析出答案列表等待用户确认，确认并给出分数后保存，然后等待用户上传学生的作业图片
模型识别学生答案并和用户的标准答案之间进行比较后打分，将分数进行总结，
用tab页进行分隔
1，tab1:用户上传答案 点击识别按钮 自动识别出答案列表（序号、标准答案、分值、评分标准），并给出每个答案默认分值为1分 用户可以修改答案分值，和评分标准（文本：相同/包含要点自评分/其他自定义）
2，tab2:用户上传学生答案图片 点击识别按钮 自动进行评分 并给出学生信息（姓名和学号如有）和 识别列表（序号、学生答案、标准答案、分值、评分标准）老师可以修改得分，然后点击保存保存到临时文件中
3，tab3:用户可以查看本次所有学生的评分情况 横坐标为(学生姓名,正确率），纵坐标为（总分，答案1，答案2.....)，可以进行清空和导出到csv
【任务】：
1，跟用户确认开发需求和技术栈，
2，跟用户进行确认MVP设计
3，开始进行MVP编码并进行测试
【输出】：
1，完整的开发计划todo
2，完整的mvp设计readme
3，完整的mvp代码
【限制】
1，需要完整的代码不能简化。