#!/usr/bin/env python3
"""
AI作业批改系统启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 9):
        print("❌ 需要Python 3.9或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    return True

def check_gpu():
    """检查GPU可用性"""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            print(f"✅ 检测到GPU: {gpu_name} (共{gpu_count}个)")
            return True
        else:
            print("⚠️ 未检测到CUDA GPU，将使用CPU运行（速度较慢）")
            return False
    except ImportError:
        print("⚠️ PyTorch未安装，无法检测GPU")
        return False

def install_dependencies():
    """安装依赖包"""
    print("📦 检查并安装依赖包...")
    
    try:
        # 检查requirements.txt是否存在
        requirements_file = Path("requirements.txt")
        if not requirements_file.exists():
            print("❌ requirements.txt文件不存在")
            return False
        
        # 安装依赖
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 依赖包安装完成")
            return True
        else:
            print(f"❌ 依赖包安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 安装依赖时出错: {e}")
        return False

def setup_directories():
    """创建必要的目录"""
    directories = [
        "uploads",
        "database", 
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"📁 创建目录: {directory}")

def check_model_availability():
    """检查模型可用性"""
    print("🤖 检查AI模型...")
    
    try:
        from transformers import AutoTokenizer
        
        model_name = "openbmb/MiniCPM-V-4-AWQ"
        
        # 尝试加载tokenizer（这会触发模型下载）
        print(f"正在检查模型: {model_name}")
        tokenizer = AutoTokenizer.from_pretrained(
            model_name, 
            trust_remote_code=True
        )
        
        print("✅ AI模型可用")
        return True
        
    except Exception as e:
        print(f"❌ AI模型检查失败: {e}")
        print("💡 首次运行时模型会自动下载，请确保网络连接正常")
        return False

def initialize_database():
    """初始化数据库"""
    print("🗄️ 初始化数据库...")
    
    try:
        from database.db_manager import init_db
        init_db()
        print("✅ 数据库初始化完成")
        return True
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False

def run_streamlit():
    """启动Streamlit应用"""
    print("🚀 启动AI作业批改系统...")
    
    try:
        # 设置Streamlit配置
        os.environ["STREAMLIT_SERVER_PORT"] = "8501"
        os.environ["STREAMLIT_SERVER_ADDRESS"] = "localhost"
        
        # 启动Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ])
        
    except KeyboardInterrupt:
        print("\n👋 系统已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def main():
    """主函数"""
    print("=" * 50)
    print("🤖 AI作业批改系统启动器")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 创建目录
    setup_directories()
    
    # 安装依赖（可选）
    install_choice = input("\n📦 是否安装/更新依赖包? (y/n): ").lower().strip()
    if install_choice in ['y', 'yes']:
        if not install_dependencies():
            print("❌ 依赖安装失败，请手动运行: pip install -r requirements.txt")
            return
    
    # 检查GPU
    check_gpu()
    
    # 检查模型（可选，因为首次运行会自动下载）
    model_choice = input("\n🤖 是否检查AI模型? (y/n): ").lower().strip()
    if model_choice in ['y', 'yes']:
        check_model_availability()
    
    # 初始化数据库
    if not initialize_database():
        print("❌ 数据库初始化失败")
        return
    
    print("\n" + "=" * 50)
    print("✅ 系统准备就绪！")
    print("🌐 浏览器将自动打开 http://localhost:8501")
    print("💡 按 Ctrl+C 停止系统")
    print("=" * 50)
    
    # 启动应用
    run_streamlit()

if __name__ == "__main__":
    main()
