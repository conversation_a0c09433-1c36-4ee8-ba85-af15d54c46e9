# AI作业批改系统 MVP设计文档

## 项目概述

基于MiniCPM-V-4-AWQ视觉大模型和Streamlit框架开发的智能作业批改系统，支持图像识别、自动评分和成绩统计分析。

## 技术栈

- **后端框架**: Python + Streamlit
- **AI模型**: MiniCPM-V-4-AWQ (本地部署)
- **推理引擎**: vLLM
- **数据库**: SQLite
- **图像处理**: Pillow
- **数据分析**: pandas, matplotlib
- **支持格式**: JPG, PNG

## 系统架构

```
├── app.py                 # Streamlit主应用
├── models/
│   ├── ai_model.py       # AI模型封装
│   └── data_models.py    # 数据模型定义
├── database/
│   ├── db_manager.py     # 数据库管理
│   └── schema.sql        # 数据库结构
├── utils/
│   ├── image_processor.py # 图像处理工具
│   ├── scoring.py        # 评分算法
│   └── export.py         # 数据导出
├── pages/
│   ├── tab1_answer_setup.py    # 标准答案设置
│   ├── tab2_grading.py         # 作业批改
│   └── tab3_statistics.py     # 成绩统计
└── requirements.txt      # 依赖包列表
```

## 核心功能设计

### Tab1: 标准答案管理
- **图片上传**: 支持JPG/PNG格式
- **AI识别**: 自动解析答案列表(序号、内容、分值)
- **用户编辑**: 修改答案、分值、评分标准
- **评分标准**: 
  - 完全相同
  - 包含关键词
  - 模糊匹配
  - 自定义规则
- **数据保存**: 存储到SQLite数据库

### Tab2: 学生作业批改
- **作业上传**: 批量上传学生作业图片
- **学生信息**: 自动识别姓名、学号
- **自动评分**: 基于标准答案进行智能评分
- **手动调整**: 教师可修改AI评分结果
- **评分详情**: 显示每题得分和评分依据
- **批量保存**: 保存到临时文件和数据库

### Tab3: 成绩统计分析
- **成绩总览**: 显示所有学生评分情况
- **数据可视化**: 
  - 横坐标: 学生姓名、正确率
  - 纵坐标: 总分、各题得分
- **统计分析**: 平均分、最高分、最低分、分数分布
- **数据导出**: 导出CSV格式成绩单
- **数据管理**: 清空当前批次数据

## 数据模型设计

### 标准答案表 (standard_answers)
```sql
CREATE TABLE standard_answers (
    id INTEGER PRIMARY KEY,
    question_no INTEGER,
    content TEXT,
    score REAL,
    grading_type TEXT,  -- 'exact', 'keyword', 'fuzzy', 'custom'
    grading_rule TEXT,
    created_at TIMESTAMP
);
```

### 学生作业表 (student_submissions)
```sql
CREATE TABLE student_submissions (
    id INTEGER PRIMARY KEY,
    student_name TEXT,
    student_id TEXT,
    image_path TEXT,
    total_score REAL,
    submitted_at TIMESTAMP
);
```

### 评分详情表 (grading_details)
```sql
CREATE TABLE grading_details (
    id INTEGER PRIMARY KEY,
    submission_id INTEGER,
    question_no INTEGER,
    student_answer TEXT,
    standard_answer TEXT,
    score_earned REAL,
    score_total REAL,
    grading_reason TEXT,
    FOREIGN KEY (submission_id) REFERENCES student_submissions(id)
);
```

## AI模型集成

### 模型配置
- **模型**: openbmb/MiniCPM-V-4-AWQ
- **推理引擎**: vLLM
- **最大长度**: 2048 tokens
- **温度**: 0.7
- **Top-p**: 0.8

### Prompt设计

#### 答案识别Prompt
```
请分析这张答案图片，提取出所有题目的标准答案。
返回JSON格式：
{
  "answers": [
    {
      "question_no": 1,
      "content": "答案内容",
      "keywords": ["关键词1", "关键词2"]
    }
  ]
}
```

#### 学生作业评分Prompt
```
请分析这张学生作业图片，识别学生信息和答案。
标准答案：{standard_answers}
返回JSON格式：
{
  "student_info": {
    "name": "学生姓名",
    "student_id": "学号"
  },
  "answers": [
    {
      "question_no": 1,
      "content": "学生答案",
      "confidence": 0.95
    }
  ]
}
```

## 评分算法设计

### 1. 完全相同匹配
```python
def exact_match(student_answer, standard_answer):
    return student_answer.strip() == standard_answer.strip()
```

### 2. 关键词匹配
```python
def keyword_match(student_answer, keywords, threshold=0.6):
    matched = sum(1 for kw in keywords if kw in student_answer)
    return matched / len(keywords) >= threshold
```

### 3. 模糊匹配
```python
from difflib import SequenceMatcher

def fuzzy_match(student_answer, standard_answer, threshold=0.8):
    similarity = SequenceMatcher(None, student_answer, standard_answer).ratio()
    return similarity >= threshold
```

## 部署要求

### 硬件要求
- **GPU**: 推荐RTX 4090或以上 (24GB显存)
- **内存**: 32GB以上
- **存储**: 50GB可用空间

### 软件依赖
- Python 3.9+
- CUDA 11.8+
- PyTorch 2.0+

### 安装步骤
```bash
# 1. 克隆项目
git clone <repository_url>
cd ai-homework-grader

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 下载模型
# 模型会自动从HuggingFace下载

# 5. 初始化数据库
python -c "from database.db_manager import init_db; init_db()"

# 6. 启动应用
streamlit run app.py
```

## MVP功能清单

### 核心功能 (必须实现)
- [x] 图片上传和预览
- [x] AI模型图像识别
- [x] 标准答案设置和编辑
- [x] 学生作业自动评分
- [x] 成绩数据存储
- [x] 基础数据可视化
- [x] CSV导出功能

### 增强功能 (后续版本)
- [ ] 批量图片处理
- [ ] 多种评分算法
- [ ] 详细的错误分析
- [ ] 学生答案相似度检测
- [ ] 历史数据管理
- [ ] 用户权限管理

## 测试计划

### 单元测试
- AI模型识别准确性测试
- 评分算法正确性测试
- 数据库操作测试

### 集成测试
- 完整流程测试
- 多用户并发测试
- 大批量数据处理测试

### 用户验收测试
- 教师使用流程测试
- 界面易用性测试
- 性能压力测试

## 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone <repository_url>
cd ai-homework-grader

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 启动系统
```bash
# 方式1: 使用启动脚本（推荐）
python run.py

# 方式2: 直接启动Streamlit
streamlit run app.py
```

### 3. 使用流程
1. 打开浏览器访问 http://localhost:8501
2. 在「标准答案设置」页面上传答案图片并设置评分规则
3. 在「学生作业批改」页面上传学生作业进行批改
4. 在「成绩统计分析」页面查看统计结果和导出数据

## 测试

### 运行测试
```bash
# 运行系统测试
python test_system.py

# 测试特定模块
python -m unittest test_system.TestDatabaseManager
```

### 测试覆盖
- 数据库操作测试
- 评分算法测试
- 图像处理测试
- 数据导出测试
- AI模型集成测试（可选）

## 项目结构详解

```
ai-homework-grader/
├── app.py                    # 主应用入口
├── run.py                    # 启动脚本
├── test_system.py           # 系统测试
├── requirements.txt         # 依赖包列表
├── README.md               # 项目说明
├── models/                 # AI模型模块
│   └── ai_model.py        # AI模型封装
├── database/              # 数据库模块
│   ├── db_manager.py     # 数据库管理
│   └── schema.sql        # 数据库结构
├── utils/                 # 工具模块
│   ├── image_processor.py # 图像处理
│   ├── scoring.py        # 评分算法
│   └── export.py         # 数据导出
├── pages/                 # 页面模块
│   ├── tab1_answer_setup.py    # 标准答案设置
│   ├── tab2_grading.py         # 作业批改
│   └── tab3_statistics.py     # 成绩统计
└── uploads/               # 上传文件目录（自动创建）
```

## 风险评估与解决方案

### 技术风险
- **AI识别准确率**: 通过优化Prompt设计和添加人工校验提高准确率
- **模型推理速度**: 使用量化模型和批处理优化性能
- **资源消耗**: 支持CPU运行，提供资源监控

### 解决方案
- 多种评分模式降低对AI识别的依赖
- 完善的手动调整功能
- 详细的错误日志和调试信息
- 渐进式功能实现，确保核心功能稳定
