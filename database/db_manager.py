import sqlite3
import os
import json
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import pandas as pd

class DatabaseManager:
    def __init__(self, db_path: str = "homework_grader.db"):
        self.db_path = db_path
        self.init_db()
    
    def init_db(self):
        """初始化数据库"""
        with sqlite3.connect(self.db_path) as conn:
            # 读取并执行schema.sql
            schema_path = os.path.join(os.path.dirname(__file__), "schema.sql")
            with open(schema_path, 'r', encoding='utf-8') as f:
                schema_sql = f.read()
            conn.executescript(schema_sql)
            conn.commit()
    
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
        return conn
    
    # ==================== 标准答案管理 ====================
    
    def save_standard_answers(self, answers: List[Dict]) -> bool:
        """保存标准答案"""
        try:
            with self.get_connection() as conn:
                # 先清空现有答案
                conn.execute("DELETE FROM standard_answers")
                
                # 插入新答案
                for answer in answers:
                    keywords_json = json.dumps(answer.get('keywords', []), ensure_ascii=False)
                    conn.execute("""
                        INSERT INTO standard_answers 
                        (question_no, content, score, grading_type, grading_rule, keywords)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        answer['question_no'],
                        answer['content'],
                        answer.get('score', 1.0),
                        answer.get('grading_type', 'exact'),
                        answer.get('grading_rule', ''),
                        keywords_json
                    ))
                conn.commit()
                return True
        except Exception as e:
            print(f"保存标准答案失败: {e}")
            return False
    
    def get_standard_answers(self) -> List[Dict]:
        """获取标准答案"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT * FROM standard_answers ORDER BY question_no
                """)
                answers = []
                for row in cursor.fetchall():
                    answer = dict(row)
                    # 解析keywords JSON
                    if answer['keywords']:
                        answer['keywords'] = json.loads(answer['keywords'])
                    else:
                        answer['keywords'] = []
                    answers.append(answer)
                return answers
        except Exception as e:
            print(f"获取标准答案失败: {e}")
            return []
    
    # ==================== 学生作业管理 ====================
    
    def save_student_submission(self, student_name: str, student_id: str, 
                              image_path: str, answers: List[Dict]) -> Optional[int]:
        """保存学生作业提交"""
        try:
            with self.get_connection() as conn:
                # 计算总分
                total_score = sum(answer.get('score_earned', 0) for answer in answers)
                max_score = sum(answer.get('score_total', 1) for answer in answers)
                accuracy = (total_score / max_score * 100) if max_score > 0 else 0
                
                # 插入学生提交记录
                cursor = conn.execute("""
                    INSERT INTO student_submissions 
                    (student_name, student_id, image_path, total_score, max_score, accuracy, graded_at, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (student_name, student_id, image_path, total_score, max_score, accuracy, 
                      datetime.now(), 'graded'))
                
                submission_id = cursor.lastrowid
                
                # 插入评分详情
                for answer in answers:
                    conn.execute("""
                        INSERT INTO grading_details 
                        (submission_id, question_no, student_answer, standard_answer, 
                         score_earned, score_total, grading_type, grading_reason, 
                         is_correct, confidence)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        submission_id,
                        answer['question_no'],
                        answer.get('student_answer', ''),
                        answer.get('standard_answer', ''),
                        answer.get('score_earned', 0),
                        answer.get('score_total', 1),
                        answer.get('grading_type', 'exact'),
                        answer.get('grading_reason', ''),
                        answer.get('is_correct', False),
                        answer.get('confidence', 0.0)
                    ))
                
                conn.commit()
                return submission_id
        except Exception as e:
            print(f"保存学生作业失败: {e}")
            return None
    
    def get_all_submissions(self) -> List[Dict]:
        """获取所有学生提交记录"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT * FROM student_submissions 
                    ORDER BY submitted_at DESC
                """)
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取学生提交记录失败: {e}")
            return []
    
    def get_submission_details(self, submission_id: int) -> List[Dict]:
        """获取特定提交的详细评分信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT * FROM grading_details 
                    WHERE submission_id = ? 
                    ORDER BY question_no
                """, (submission_id,))
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取评分详情失败: {e}")
            return []
    
    def update_manual_score(self, detail_id: int, new_score: float, reason: str = "") -> bool:
        """手动调整分数"""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    UPDATE grading_details 
                    SET score_earned = ?, grading_reason = ?, manual_adjusted = TRUE
                    WHERE id = ?
                """, (new_score, reason, detail_id))
                
                # 重新计算总分
                cursor = conn.execute("""
                    SELECT submission_id FROM grading_details WHERE id = ?
                """, (detail_id,))
                submission_id = cursor.fetchone()[0]
                
                cursor = conn.execute("""
                    SELECT SUM(score_earned) as total, SUM(score_total) as max_total
                    FROM grading_details WHERE submission_id = ?
                """, (submission_id,))
                result = cursor.fetchone()
                total_score = result[0] or 0
                max_score = result[1] or 1
                accuracy = (total_score / max_score * 100) if max_score > 0 else 0
                
                conn.execute("""
                    UPDATE student_submissions 
                    SET total_score = ?, accuracy = ?
                    WHERE id = ?
                """, (total_score, accuracy, submission_id))
                
                conn.commit()
                return True
        except Exception as e:
            print(f"更新分数失败: {e}")
            return False
    
    # ==================== 统计分析 ====================
    
    def get_statistics_data(self) -> pd.DataFrame:
        """获取统计分析数据"""
        try:
            with self.get_connection() as conn:
                query = """
                    SELECT 
                        s.student_name,
                        s.student_id,
                        s.total_score,
                        s.max_score,
                        s.accuracy,
                        s.submitted_at,
                        GROUP_CONCAT(d.score_earned || '/' || d.score_total) as question_scores
                    FROM student_submissions s
                    LEFT JOIN grading_details d ON s.id = d.submission_id
                    GROUP BY s.id
                    ORDER BY s.submitted_at DESC
                """
                return pd.read_sql_query(query, conn)
        except Exception as e:
            print(f"获取统计数据失败: {e}")
            return pd.DataFrame()
    
    def clear_all_data(self) -> bool:
        """清空所有数据"""
        try:
            with self.get_connection() as conn:
                conn.execute("DELETE FROM grading_details")
                conn.execute("DELETE FROM student_submissions")
                conn.execute("DELETE FROM standard_answers")
                conn.commit()
                return True
        except Exception as e:
            print(f"清空数据失败: {e}")
            return False

# 全局数据库实例
db_manager = DatabaseManager()

def init_db():
    """初始化数据库的便捷函数"""
    db_manager.init_db()
    print("数据库初始化完成")
