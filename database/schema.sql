-- 标准答案表
CREATE TABLE IF NOT EXISTS standard_answers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    question_no INTEGER NOT NULL,
    content TEXT NOT NULL,
    score REAL DEFAULT 1.0,
    grading_type TEXT DEFAULT 'exact',  -- 'exact', 'keyword', 'fuzzy', 'custom'
    grading_rule TEXT,  -- 存储关键词或自定义规则
    keywords TEXT,  -- JSON格式存储关键词列表
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 学生作业提交表
CREATE TABLE IF NOT EXISTS student_submissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_name TEXT,
    student_id TEXT,
    image_path TEXT NOT NULL,
    total_score REAL DEFAULT 0.0,
    max_score REAL DEFAULT 0.0,
    accuracy REAL DEFAULT 0.0,  -- 正确率
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    graded_at TIMESTAMP,
    status TEXT DEFAULT 'pending'  -- 'pending', 'graded', 'reviewed'
);

-- 评分详情表
CREATE TABLE IF NOT EXISTS grading_details (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    submission_id INTEGER NOT NULL,
    question_no INTEGER NOT NULL,
    student_answer TEXT,
    standard_answer TEXT,
    score_earned REAL DEFAULT 0.0,
    score_total REAL DEFAULT 1.0,
    grading_type TEXT,
    grading_reason TEXT,  -- 评分依据说明
    is_correct BOOLEAN DEFAULT FALSE,
    confidence REAL DEFAULT 0.0,  -- AI识别置信度
    manual_adjusted BOOLEAN DEFAULT FALSE,  -- 是否手动调整过
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (submission_id) REFERENCES student_submissions(id) ON DELETE CASCADE
);

-- 批次管理表（用于管理不同的批改批次）
CREATE TABLE IF NOT EXISTS grading_batches (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    batch_name TEXT NOT NULL,
    description TEXT,
    total_questions INTEGER DEFAULT 0,
    total_students INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- 创建索引提高查询性能
CREATE INDEX IF NOT EXISTS idx_standard_answers_question_no ON standard_answers(question_no);
CREATE INDEX IF NOT EXISTS idx_student_submissions_student_id ON student_submissions(student_id);
CREATE INDEX IF NOT EXISTS idx_grading_details_submission_id ON grading_details(submission_id);
CREATE INDEX IF NOT EXISTS idx_grading_details_question_no ON grading_details(question_no);
