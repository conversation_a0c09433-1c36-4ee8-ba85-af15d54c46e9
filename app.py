import streamlit as st
import os
import sys
from pathlib import Path

# 设置页面配置
st.set_page_config(
    page_title="AI作业批改系统",
    page_icon="📝",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 导入页面模块
from pages.tab1_answer_setup import render_tab1
from pages.tab2_grading import render_tab2
from pages.tab3_statistics import render_tab3
from database.db_manager import init_db

def main():
    """主应用函数"""
    
    # 初始化数据库
    try:
        init_db()
    except Exception as e:
        st.error(f"数据库初始化失败: {e}")
        return
    
    # 应用标题和描述
    st.title("🤖 AI作业批改系统")
    st.markdown("""
    基于 **MiniCPM-V-4-AWQ** 视觉大模型的智能作业批改系统
    
    **功能特点:**
    - 📝 智能识别标准答案和学生作业
    - 🎯 多种评分模式：完全匹配、关键词匹配、模糊匹配、自定义规则
    - 📊 详细的成绩统计和数据可视化
    - 📥 支持数据导出和批量处理
    """)
    
    # 侧边栏 - 系统信息
    with st.sidebar:
        st.header("🔧 系统信息")
        
        # 显示系统状态
        display_system_status()
        
        st.markdown("---")
        
        # 使用说明
        with st.expander("📖 使用说明", expanded=False):
            st.markdown("""
            **使用流程:**
            
            1. **标准答案设置**
               - 上传答案图片
               - AI自动识别答案
               - 编辑和确认答案
               - 设置评分规则
            
            2. **学生作业批改**
               - 上传学生作业图片
               - AI自动识别和评分
               - 教师手动调整分数
               - 保存评分结果
            
            3. **成绩统计分析**
               - 查看所有学生成绩
               - 数据可视化分析
               - 导出成绩报告
            """)
        
        # 技术信息
        with st.expander("⚙️ 技术信息", expanded=False):
            st.markdown("""
            **技术栈:**
            - AI模型: MiniCPM-V-4-AWQ
            - 推理引擎: vLLM
            - 前端框架: Streamlit
            - 数据库: SQLite
            - 图像处理: Pillow
            
            **支持格式:**
            - 图片: JPG, PNG
            - 导出: CSV
            """)
    
    # 主要内容区域 - Tab导航
    tab1, tab2, tab3 = st.tabs([
        "📝 标准答案设置", 
        "📊 学生作业批改", 
        "📈 成绩统计分析"
    ])
    
    with tab1:
        render_tab1()
    
    with tab2:
        render_tab2()
    
    with tab3:
        render_tab3()
    
    # 页脚
    st.markdown("---")
    st.markdown(
        """
        <div style='text-align: center; color: #666; font-size: 14px;'>
            🤖 AI作业批改系统 | 基于MiniCPM-V-4-AWQ视觉大模型 | 
            <a href='https://github.com/OpenBMB/MiniCPM-V' target='_blank'>了解更多</a>
        </div>
        """, 
        unsafe_allow_html=True
    )

def display_system_status():
    """显示系统状态"""
    
    # 检查AI模型状态
    try:
        from models.ai_model import get_ai_model
        ai_model = get_ai_model()
        if ai_model:
            st.success("✅ AI模型已加载")
        else:
            st.error("❌ AI模型未加载")
    except Exception as e:
        st.error(f"❌ AI模型错误: {str(e)[:50]}...")
    
    # 检查数据库状态
    try:
        from database.db_manager import db_manager
        standard_answers = db_manager.get_standard_answers()
        submissions = db_manager.get_all_submissions()
        
        st.info(f"📚 标准答案: {len(standard_answers)} 题")
        st.info(f"📋 已批改作业: {len(submissions)} 份")
        
    except Exception as e:
        st.error(f"❌ 数据库错误: {str(e)[:50]}...")
    
    # 检查上传目录
    upload_dir = "uploads"
    if os.path.exists(upload_dir):
        file_count = len([f for f in os.listdir(upload_dir) if os.path.isfile(os.path.join(upload_dir, f))])
        st.info(f"📁 上传文件: {file_count} 个")
    else:
        st.warning("⚠️ 上传目录不存在")

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'streamlit',
        'vllm', 
        'transformers',
        'torch',
        'Pillow',
        'pandas',
        'matplotlib',
        'plotly'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        st.error(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        st.markdown("""
        请运行以下命令安装依赖:
        ```bash
        pip install -r requirements.txt
        ```
        """)
        return False
    
    return True

def setup_environment():
    """设置环境"""
    
    # 创建必要的目录
    directories = ['uploads', 'database']
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"创建目录: {directory}")
    
    # 设置matplotlib中文字体
    try:
        import matplotlib.pyplot as plt
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
    except:
        pass

if __name__ == "__main__":
    # 检查依赖
    if not check_dependencies():
        st.stop()
    
    # 设置环境
    setup_environment()
    
    # 运行主应用
    main()
