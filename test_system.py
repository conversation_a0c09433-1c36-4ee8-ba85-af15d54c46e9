#!/usr/bin/env python3
"""
AI作业批改系统测试脚本
"""

import os
import sys
import unittest
from pathlib import Path
import tempfile
import shutil
from PIL import Image
import numpy as np

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

class TestDatabaseManager(unittest.TestCase):
    """测试数据库管理器"""
    
    def setUp(self):
        """测试前准备"""
        self.test_db = "test_homework.db"
        
        # 导入数据库管理器
        from database.db_manager import DatabaseManager
        self.db_manager = DatabaseManager(self.test_db)
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.test_db):
            os.remove(self.test_db)
    
    def test_save_and_get_standard_answers(self):
        """测试保存和获取标准答案"""
        test_answers = [
            {
                'question_no': 1,
                'content': '测试答案1',
                'keywords': ['关键词1', '关键词2'],
                'score': 2.0,
                'grading_type': 'keyword'
            },
            {
                'question_no': 2,
                'content': '测试答案2',
                'keywords': ['关键词3'],
                'score': 3.0,
                'grading_type': 'exact'
            }
        ]
        
        # 保存答案
        success = self.db_manager.save_standard_answers(test_answers)
        self.assertTrue(success)
        
        # 获取答案
        saved_answers = self.db_manager.get_standard_answers()
        self.assertEqual(len(saved_answers), 2)
        self.assertEqual(saved_answers[0]['content'], '测试答案1')
        self.assertEqual(saved_answers[1]['score'], 3.0)
    
    def test_save_student_submission(self):
        """测试保存学生提交"""
        test_answers = [
            {
                'question_no': 1,
                'student_answer': '学生答案1',
                'standard_answer': '标准答案1',
                'score_earned': 1.5,
                'score_total': 2.0,
                'is_correct': False,
                'grading_type': 'keyword',
                'grading_reason': '部分正确'
            }
        ]
        
        submission_id = self.db_manager.save_student_submission(
            student_name="张三",
            student_id="20230001",
            image_path="test.jpg",
            answers=test_answers
        )
        
        self.assertIsNotNone(submission_id)
        
        # 获取提交详情
        details = self.db_manager.get_submission_details(submission_id)
        self.assertEqual(len(details), 1)
        self.assertEqual(details[0]['student_answer'], '学生答案1')

class TestScoringEngine(unittest.TestCase):
    """测试评分引擎"""
    
    def setUp(self):
        """测试前准备"""
        from utils.scoring import ScoringEngine
        self.scoring_engine = ScoringEngine()
    
    def test_exact_match(self):
        """测试完全匹配"""
        standard_answer = {
            'content': '北京是中国的首都',
            'score': 2.0,
            'grading_type': 'exact'
        }
        
        # 完全匹配
        result = self.scoring_engine.score_answer('北京是中国的首都', standard_answer)
        self.assertEqual(result['score_earned'], 2.0)
        self.assertTrue(result['is_correct'])
        
        # 不匹配
        result = self.scoring_engine.score_answer('上海是中国的首都', standard_answer)
        self.assertEqual(result['score_earned'], 0.0)
        self.assertFalse(result['is_correct'])
    
    def test_keyword_match(self):
        """测试关键词匹配"""
        standard_answer = {
            'content': '北京是中国的首都',
            'keywords': ['北京', '首都', '中国'],
            'score': 3.0,
            'grading_type': 'keyword'
        }
        
        # 包含所有关键词
        result = self.scoring_engine.score_answer('北京是中国的首都城市', standard_answer)
        self.assertEqual(result['score_earned'], 3.0)
        self.assertTrue(result['is_correct'])
        
        # 包含部分关键词
        result = self.scoring_engine.score_answer('北京是首都', standard_answer)
        self.assertGreater(result['score_earned'], 0)
        self.assertLess(result['score_earned'], 3.0)
    
    def test_fuzzy_match(self):
        """测试模糊匹配"""
        standard_answer = {
            'content': '地球绕太阳转',
            'score': 2.0,
            'grading_type': 'fuzzy'
        }
        
        # 高相似度
        result = self.scoring_engine.score_answer('地球围绕太阳转动', standard_answer)
        self.assertGreater(result['score_earned'], 0)
        self.assertGreater(result['similarity'], 0.5)

class TestImageProcessor(unittest.TestCase):
    """测试图像处理器"""
    
    def setUp(self):
        """测试前准备"""
        from utils.image_processor import ImageProcessor
        self.temp_dir = tempfile.mkdtemp()
        self.image_processor = ImageProcessor(self.temp_dir)
    
    def tearDown(self):
        """测试后清理"""
        shutil.rmtree(self.temp_dir)
    
    def test_create_test_image(self):
        """创建测试图像"""
        # 创建一个简单的测试图像
        image = Image.new('RGB', (100, 100), color='red')
        return image
    
    def test_preprocess_image(self):
        """测试图像预处理"""
        test_image = self.test_create_test_image()
        
        # 预处理图像
        processed = self.image_processor.preprocess_image(test_image)
        
        self.assertIsInstance(processed, Image.Image)
        self.assertEqual(processed.mode, 'RGB')
    
    def test_get_image_info(self):
        """测试获取图像信息"""
        test_image = self.test_create_test_image()
        
        info = self.image_processor.get_image_info(test_image)
        
        self.assertEqual(info['size'], (100, 100))
        self.assertEqual(info['mode'], 'RGB')
        self.assertEqual(info['width'], 100)
        self.assertEqual(info['height'], 100)

class TestDataExporter(unittest.TestCase):
    """测试数据导出器"""
    
    def setUp(self):
        """测试前准备"""
        from utils.export import DataExporter
        self.exporter = DataExporter()
    
    def test_export_to_csv(self):
        """测试CSV导出"""
        test_data = [
            {'name': '张三', 'score': 85, 'grade': 'A'},
            {'name': '李四', 'score': 92, 'grade': 'A+'},
            {'name': '王五', 'score': 78, 'grade': 'B'}
        ]
        
        csv_string = self.exporter.export_to_csv(test_data)
        
        self.assertIn('张三', csv_string)
        self.assertIn('85', csv_string)
        self.assertIn('name,score,grade', csv_string)
    
    def test_export_standard_answers(self):
        """测试标准答案导出"""
        test_answers = [
            {
                'question_no': 1,
                'content': '测试答案',
                'score': 2.0,
                'grading_type': 'exact',
                'keywords': ['关键词1', '关键词2']
            }
        ]
        
        csv_string = self.exporter.export_standard_answers(test_answers)
        
        self.assertIn('测试答案', csv_string)
        self.assertIn('关键词1', csv_string)

def run_basic_tests():
    """运行基础测试"""
    print("🧪 运行基础功能测试...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_suite.addTest(unittest.makeSuite(TestDatabaseManager))
    test_suite.addTest(unittest.makeSuite(TestScoringEngine))
    test_suite.addTest(unittest.makeSuite(TestImageProcessor))
    test_suite.addTest(unittest.makeSuite(TestDataExporter))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()

def test_ai_model():
    """测试AI模型（可选）"""
    print("🤖 测试AI模型...")
    
    try:
        from models.ai_model import HomeworkGraderAI
        
        # 创建测试图像
        test_image = Image.new('RGB', (200, 200), color='white')
        
        # 初始化AI模型（这可能需要一些时间）
        print("正在加载AI模型...")
        ai_model = HomeworkGraderAI()
        
        print("✅ AI模型加载成功")
        return True
        
    except Exception as e:
        print(f"❌ AI模型测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("🧪 AI作业批改系统测试")
    print("=" * 50)
    
    # 运行基础测试
    basic_success = run_basic_tests()
    
    if basic_success:
        print("✅ 基础功能测试通过")
    else:
        print("❌ 基础功能测试失败")
    
    # 询问是否测试AI模型
    test_ai = input("\n🤖 是否测试AI模型? (y/n): ").lower().strip()
    ai_success = True
    
    if test_ai in ['y', 'yes']:
        ai_success = test_ai_model()
    
    # 总结
    print("\n" + "=" * 50)
    if basic_success and ai_success:
        print("🎉 所有测试通过！系统可以正常使用")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")
    print("=" * 50)

if __name__ == "__main__":
    main()
