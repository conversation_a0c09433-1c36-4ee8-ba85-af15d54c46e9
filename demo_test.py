#!/usr/bin/env python3
"""
AI作业批改系统演示脚本
用于测试系统基本功能，无需AI模型
"""

import os
import sys
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import json

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

def create_demo_answer_image():
    """创建演示用的答案图片"""
    # 创建白色背景图片
    width, height = 800, 600
    image = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(image)
    
    # 尝试使用系统字体
    try:
        font = ImageFont.truetype("arial.ttf", 24)
        title_font = ImageFont.truetype("arial.ttf", 32)
    except:
        font = ImageFont.load_default()
        title_font = ImageFont.load_default()
    
    # 绘制标题
    draw.text((50, 30), "标准答案", fill='black', font=title_font)
    
    # 绘制答案
    answers = [
        "1. 北京是中国的首都",
        "2. 地球绕太阳转动",
        "3. 水的化学式是H2O",
        "4. 1+1=2",
        "5. 中国有56个民族"
    ]
    
    y_pos = 100
    for answer in answers:
        draw.text((50, y_pos), answer, fill='black', font=font)
        y_pos += 60
    
    # 保存图片
    demo_dir = Path("demo_images")
    demo_dir.mkdir(exist_ok=True)
    
    image_path = demo_dir / "demo_answer.png"
    image.save(image_path)
    
    print(f"✅ 创建演示答案图片: {image_path}")
    return str(image_path)

def create_demo_student_image():
    """创建演示用的学生作业图片"""
    # 创建白色背景图片
    width, height = 800, 600
    image = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(image)
    
    # 尝试使用系统字体
    try:
        font = ImageFont.truetype("arial.ttf", 24)
        title_font = ImageFont.truetype("arial.ttf", 32)
        small_font = ImageFont.truetype("arial.ttf", 18)
    except:
        font = ImageFont.load_default()
        title_font = ImageFont.load_default()
        small_font = ImageFont.load_default()
    
    # 绘制学生信息
    draw.text((50, 30), "学生作业", fill='black', font=title_font)
    draw.text((50, 70), "姓名: 张三", fill='black', font=small_font)
    draw.text((200, 70), "学号: 20230001", fill='black', font=small_font)
    
    # 绘制学生答案
    student_answers = [
        "1. 北京是中国首都",  # 部分正确
        "2. 地球围绕太阳转",  # 基本正确
        "3. H2O是水",        # 顺序不同但正确
        "4. 一加一等于二",    # 表达不同但正确
        "5. 中国有很多民族"   # 不够准确
    ]
    
    y_pos = 120
    for answer in student_answers:
        draw.text((50, y_pos), answer, fill='black', font=font)
        y_pos += 60
    
    # 保存图片
    demo_dir = Path("demo_images")
    demo_dir.mkdir(exist_ok=True)
    
    image_path = demo_dir / "demo_student.png"
    image.save(image_path)
    
    print(f"✅ 创建演示学生作业图片: {image_path}")
    return str(image_path)

def test_database_operations():
    """测试数据库操作"""
    print("\n🗄️ 测试数据库操作...")
    
    try:
        from database.db_manager import db_manager
        
        # 测试标准答案保存
        test_answers = [
            {
                'question_no': 1,
                'content': '北京是中国的首都',
                'keywords': ['北京', '中国', '首都'],
                'score': 2.0,
                'grading_type': 'keyword'
            },
            {
                'question_no': 2,
                'content': '地球绕太阳转动',
                'keywords': ['地球', '太阳', '转动'],
                'score': 2.0,
                'grading_type': 'fuzzy'
            }
        ]
        
        success = db_manager.save_standard_answers(test_answers)
        if success:
            print("✅ 标准答案保存成功")
        else:
            print("❌ 标准答案保存失败")
            return False
        
        # 测试获取标准答案
        saved_answers = db_manager.get_standard_answers()
        print(f"✅ 获取到 {len(saved_answers)} 个标准答案")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def test_scoring_engine():
    """测试评分引擎"""
    print("\n🎯 测试评分引擎...")
    
    try:
        from utils.scoring import scoring_engine
        
        # 测试数据
        standard_answer = {
            'content': '北京是中国的首都',
            'keywords': ['北京', '中国', '首都'],
            'score': 2.0,
            'grading_type': 'keyword'
        }
        
        test_cases = [
            ('北京是中国的首都', '完全匹配'),
            ('北京是中国首都', '部分匹配'),
            ('上海是中国的首都', '错误答案'),
            ('北京 中国 首都', '关键词匹配')
        ]
        
        for student_answer, description in test_cases:
            result = scoring_engine.score_answer(student_answer, standard_answer)
            print(f"  {description}: {result['score_earned']:.1f}/{result['score_total']:.1f} - {result['grading_reason']}")
        
        print("✅ 评分引擎测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 评分引擎测试失败: {e}")
        return False

def test_image_processing():
    """测试图像处理"""
    print("\n🖼️ 测试图像处理...")
    
    try:
        from utils.image_processor import image_processor
        
        # 创建测试图片
        answer_image_path = create_demo_answer_image()
        student_image_path = create_demo_student_image()
        
        # 测试图片加载
        answer_image = image_processor.load_image(answer_image_path)
        student_image = image_processor.load_image(student_image_path)
        
        if answer_image and student_image:
            print("✅ 图片加载成功")
            
            # 测试图片预处理
            processed_answer = image_processor.preprocess_image(answer_image)
            processed_student = image_processor.preprocess_image(student_image)
            
            print("✅ 图片预处理成功")
            
            # 获取图片信息
            info = image_processor.get_image_info(processed_answer)
            print(f"✅ 图片信息: {info['width']}x{info['height']}, {info['mode']}")
            
            return True
        else:
            print("❌ 图片加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 图像处理测试失败: {e}")
        return False

def test_data_export():
    """测试数据导出"""
    print("\n📥 测试数据导出...")
    
    try:
        from utils.export import data_exporter
        
        # 测试数据
        test_data = [
            {'学生姓名': '张三', '总分': 8.5, '正确率': 85.0},
            {'学生姓名': '李四', '总分': 9.2, '正确率': 92.0},
            {'学生姓名': '王五', '总分': 7.8, '正确率': 78.0}
        ]
        
        # 导出CSV
        csv_data = data_exporter.export_to_csv(test_data)
        
        if csv_data and '张三' in csv_data:
            print("✅ CSV导出成功")
            
            # 保存到文件
            with open('demo_export.csv', 'w', encoding='utf-8-sig') as f:
                f.write(csv_data)
            print("✅ CSV文件保存成功: demo_export.csv")
            
            return True
        else:
            print("❌ CSV导出失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据导出测试失败: {e}")
        return False

def simulate_grading_workflow():
    """模拟完整的批改流程"""
    print("\n🔄 模拟完整批改流程...")
    
    try:
        from database.db_manager import db_manager
        from utils.scoring import scoring_engine
        
        # 1. 设置标准答案
        standard_answers = [
            {
                'question_no': 1,
                'content': '北京是中国的首都',
                'keywords': ['北京', '中国', '首都'],
                'score': 2.0,
                'grading_type': 'keyword'
            },
            {
                'question_no': 2,
                'content': '地球绕太阳转动',
                'keywords': ['地球', '太阳', '转动'],
                'score': 2.0,
                'grading_type': 'fuzzy'
            }
        ]
        
        db_manager.save_standard_answers(standard_answers)
        print("✅ 步骤1: 标准答案设置完成")
        
        # 2. 模拟学生答案
        student_answers = [
            '北京是中国首都',  # 第1题
            '地球围绕太阳转'   # 第2题
        ]
        
        # 3. 评分
        grading_results = []
        total_score = 0
        max_score = 0
        
        for i, student_answer in enumerate(student_answers):
            standard = standard_answers[i]
            result = scoring_engine.score_answer(student_answer, standard)
            
            grading_result = {
                'question_no': standard['question_no'],
                'student_answer': student_answer,
                'standard_answer': standard['content'],
                'score_earned': result['score_earned'],
                'score_total': result['score_total'],
                'is_correct': result['is_correct'],
                'grading_type': result['grading_type'],
                'grading_reason': result['grading_reason']
            }
            
            grading_results.append(grading_result)
            total_score += result['score_earned']
            max_score += result['score_total']
        
        print("✅ 步骤2: 自动评分完成")
        
        # 4. 保存结果
        submission_id = db_manager.save_student_submission(
            student_name="张三",
            student_id="20230001",
            image_path="demo_student.png",
            answers=grading_results
        )
        
        if submission_id:
            print("✅ 步骤3: 评分结果保存完成")
            
            # 5. 查看结果
            accuracy = (total_score / max_score * 100) if max_score > 0 else 0
            print(f"✅ 步骤4: 批改完成 - 总分: {total_score:.1f}/{max_score:.1f} ({accuracy:.1f}%)")
            
            return True
        else:
            print("❌ 评分结果保存失败")
            return False
            
    except Exception as e:
        print(f"❌ 批改流程模拟失败: {e}")
        return False

def main():
    """主演示函数"""
    print("=" * 60)
    print("🎭 AI作业批改系统演示")
    print("=" * 60)
    
    # 测试列表
    tests = [
        ("数据库操作", test_database_operations),
        ("评分引擎", test_scoring_engine),
        ("图像处理", test_image_processing),
        ("数据导出", test_data_export),
        ("完整流程", simulate_grading_workflow)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！系统基本功能正常")
        print("\n💡 下一步:")
        print("1. 运行 'python run.py' 启动完整系统")
        print("2. 访问 http://localhost:8501 使用Web界面")
        print("3. 上传真实的答案和作业图片进行测试")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
