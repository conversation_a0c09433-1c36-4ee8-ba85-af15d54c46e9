import streamlit as st
import json
from PIL import Image
from typing import List, Dict
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.ai_model import get_ai_model
from database.db_manager import db_manager
from utils.image_processor import image_processor
from utils.export import data_exporter

def render_tab1():
    """渲染Tab1 - 标准答案管理页面"""
    
    st.header("📝 标准答案设置")
    
    # 侧边栏 - 操作选项
    with st.sidebar:
        st.subheader("操作选项")
        
        # 导入/导出标准答案
        if st.button("📤 导出当前标准答案"):
            export_current_answers()
        
        st.markdown("---")
        
        uploaded_csv = st.file_uploader(
            "📥 导入标准答案 (CSV)",
            type=['csv'],
            help="上传之前导出的标准答案CSV文件"
        )
        
        if uploaded_csv is not None:
            import_answers_from_csv(uploaded_csv)
    
    # 主要内容区域
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.subheader("1. 上传答案图片")
        
        uploaded_file = st.file_uploader(
            "选择答案图片",
            type=['jpg', 'jpeg', 'png'],
            help="支持JPG、PNG格式，建议图片清晰度高，文字清楚"
        )
        
        if uploaded_file is not None:
            # 验证图片
            if image_processor.validate_image(uploaded_file):
                # 显示图片预览
                image = Image.open(uploaded_file)
                st.image(image, caption="答案图片预览", use_column_width=True)
                
                # 保存图片
                if 'answer_image_path' not in st.session_state:
                    image_path = image_processor.save_uploaded_image(uploaded_file, "answer")
                    if image_path:
                        st.session_state.answer_image_path = image_path
                        st.success("✅ 图片上传成功！")
                    else:
                        st.error("❌ 图片保存失败")
                
                # AI识别按钮
                if st.button("🤖 AI识别答案", type="primary", use_container_width=True):
                    recognize_answers()
            else:
                st.error("❌ 无效的图片文件，请上传JPG或PNG格式的图片")
    
    with col2:
        st.subheader("2. AI识别结果")
        
        # 显示识别进度
        if 'recognizing' in st.session_state and st.session_state.recognizing:
            with st.spinner("🤖 AI正在识别答案，请稍候..."):
                st.info("正在分析图片内容，提取标准答案...")
        
        # 显示识别结果
        if 'recognized_answers' in st.session_state:
            display_recognition_results()
    
    # 答案编辑区域
    st.markdown("---")
    st.subheader("3. 编辑标准答案")
    
    if 'answers_data' in st.session_state:
        edit_answers()
    else:
        # 加载已保存的答案
        load_saved_answers()
    
    # 保存按钮
    if 'answers_data' in st.session_state and st.session_state.answers_data:
        st.markdown("---")
        col1, col2, col3 = st.columns([1, 1, 1])
        
        with col2:
            if st.button("💾 保存标准答案", type="primary", use_container_width=True):
                save_answers()

def recognize_answers():
    """AI识别答案"""
    if 'answer_image_path' not in st.session_state:
        st.error("请先上传答案图片")
        return
    
    try:
        st.session_state.recognizing = True
        st.rerun()
        
        # 加载AI模型
        ai_model = get_ai_model()
        
        # 加载图片
        image = image_processor.load_image(st.session_state.answer_image_path)
        if image is None:
            st.error("无法加载图片")
            return
        
        # 预处理图片
        processed_image = image_processor.preprocess_image(image)
        
        # AI识别
        recognized_answers = ai_model.extract_standard_answers(processed_image)
        
        if recognized_answers:
            st.session_state.recognized_answers = recognized_answers
            st.session_state.answers_data = recognized_answers.copy()
            st.success(f"✅ 成功识别出 {len(recognized_answers)} 道题的答案！")
        else:
            st.warning("⚠️ 未能识别出答案，请检查图片质量或手动添加")
            st.session_state.answers_data = []
        
    except Exception as e:
        st.error(f"❌ AI识别失败: {str(e)}")
        st.session_state.answers_data = []
    
    finally:
        st.session_state.recognizing = False
        st.rerun()

def display_recognition_results():
    """显示AI识别结果"""
    if 'recognized_answers' not in st.session_state:
        return
    
    answers = st.session_state.recognized_answers
    
    if not answers:
        st.info("暂无识别结果")
        return
    
    st.success(f"✅ 识别出 {len(answers)} 道题")
    
    # 显示识别摘要
    with st.expander("📋 识别结果摘要", expanded=True):
        for i, answer in enumerate(answers[:3]):  # 只显示前3题
            st.write(f"**第{answer.get('question_no', i+1)}题:** {answer.get('content', '')[:50]}...")
        
        if len(answers) > 3:
            st.write(f"... 还有 {len(answers) - 3} 道题")

def edit_answers():
    """编辑答案界面"""
    answers = st.session_state.answers_data
    
    if not answers:
        st.info("暂无答案数据，请先上传图片并进行AI识别，或手动添加答案")
        
        if st.button("➕ 手动添加第一题"):
            st.session_state.answers_data = [{
                'question_no': 1,
                'content': '',
                'keywords': [],
                'score': 1.0,
                'grading_type': 'exact',
                'grading_rule': ''
            }]
            st.rerun()
        return
    
    # 答案编辑表单
    st.write(f"当前共有 **{len(answers)}** 道题")
    
    # 批量操作
    col1, col2, col3 = st.columns([1, 1, 1])
    with col1:
        if st.button("➕ 添加新题"):
            new_question = {
                'question_no': len(answers) + 1,
                'content': '',
                'keywords': [],
                'score': 1.0,
                'grading_type': 'exact',
                'grading_rule': ''
            }
            st.session_state.answers_data.append(new_question)
            st.rerun()
    
    with col2:
        if st.button("🔄 重新排序"):
            # 按题号重新排序
            st.session_state.answers_data.sort(key=lambda x: x.get('question_no', 0))
            st.rerun()
    
    # 逐题编辑
    for i, answer in enumerate(answers):
        with st.expander(f"📝 第 {answer.get('question_no', i+1)} 题", expanded=i < 2):
            
            col1, col2 = st.columns([3, 1])
            
            with col1:
                # 题号
                question_no = st.number_input(
                    "题号",
                    min_value=1,
                    value=answer.get('question_no', i+1),
                    key=f"q_no_{i}"
                )
                answer['question_no'] = question_no
                
                # 答案内容
                content = st.text_area(
                    "标准答案",
                    value=answer.get('content', ''),
                    height=100,
                    key=f"content_{i}",
                    help="输入完整的标准答案"
                )
                answer['content'] = content
                
                # 关键词
                keywords_str = st.text_input(
                    "关键词 (用逗号分隔)",
                    value=', '.join(answer.get('keywords', [])),
                    key=f"keywords_{i}",
                    help="用于关键词匹配评分的关键词，用逗号分隔"
                )
                answer['keywords'] = [kw.strip() for kw in keywords_str.split(',') if kw.strip()]
            
            with col2:
                # 分值
                score = st.number_input(
                    "分值",
                    min_value=0.0,
                    max_value=100.0,
                    value=float(answer.get('score', 1.0)),
                    step=0.5,
                    key=f"score_{i}"
                )
                answer['score'] = score
                
                # 评分类型
                grading_options = {
                    'exact': '完全匹配',
                    'keyword': '关键词匹配',
                    'fuzzy': '模糊匹配',
                    'custom': '自定义规则'
                }

                grading_type = st.selectbox(
                    "评分类型",
                    options=list(grading_options.keys()),
                    format_func=lambda x: grading_options[x],
                    index=list(grading_options.keys()).index(answer.get('grading_type', 'exact')),
                    key=f"grading_type_{i}",
                    help="选择评分方式"
                )
                answer['grading_type'] = grading_type
                
                # 自定义规则（仅当评分类型为custom时显示）
                if grading_type == 'custom':
                    grading_rule = st.text_area(
                        "自定义规则",
                        value=answer.get('grading_rule', ''),
                        height=60,
                        key=f"grading_rule_{i}",
                        help="例如：包含：关键词1、关键词2"
                    )
                    answer['grading_rule'] = grading_rule
                
                # 删除按钮
                if st.button(f"🗑️ 删除", key=f"delete_{i}"):
                    st.session_state.answers_data.pop(i)
                    st.rerun()

def save_answers():
    """保存答案到数据库"""
    try:
        answers = st.session_state.answers_data
        
        # 验证数据
        if not answers:
            st.error("没有答案数据可保存")
            return
        
        # 检查必填字段
        for i, answer in enumerate(answers):
            if not answer.get('content', '').strip():
                st.error(f"第{answer.get('question_no', i+1)}题的答案内容不能为空")
                return
        
        # 保存到数据库
        success = db_manager.save_standard_answers(answers)
        
        if success:
            st.success("✅ 标准答案保存成功！")
            st.balloons()
        else:
            st.error("❌ 保存失败，请重试")
    
    except Exception as e:
        st.error(f"❌ 保存失败: {str(e)}")

def load_saved_answers():
    """加载已保存的答案"""
    try:
        saved_answers = db_manager.get_standard_answers()
        
        if saved_answers:
            st.info(f"📚 已加载 {len(saved_answers)} 道题的标准答案")
            
            if st.button("📖 查看/编辑已保存的答案"):
                st.session_state.answers_data = saved_answers
                st.rerun()
        else:
            st.info("💡 暂无已保存的标准答案，请上传图片进行AI识别或手动添加")
    
    except Exception as e:
        st.error(f"❌ 加载答案失败: {str(e)}")

def export_current_answers():
    """导出当前标准答案"""
    try:
        answers = db_manager.get_standard_answers()
        
        if not answers:
            st.warning("暂无标准答案可导出")
            return
        
        csv_data = data_exporter.export_standard_answers(answers)
        data_exporter.create_download_link(csv_data, "标准答案")
        
        st.success("✅ 导出文件已准备就绪")
    
    except Exception as e:
        st.error(f"❌ 导出失败: {str(e)}")

def import_answers_from_csv(uploaded_csv):
    """从CSV导入标准答案"""
    try:
        imported_answers = data_exporter.import_standard_answers(uploaded_csv)
        
        if imported_answers:
            st.success(f"✅ 成功导入 {len(imported_answers)} 道题的标准答案")
            
            if st.button("📥 应用导入的答案"):
                st.session_state.answers_data = imported_answers
                st.rerun()
        else:
            st.error("❌ 导入失败，请检查CSV文件格式")
    
    except Exception as e:
        st.error(f"❌ 导入失败: {str(e)}")
