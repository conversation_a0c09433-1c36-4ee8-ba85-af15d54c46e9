import streamlit as st
import json
from PIL import Image
from typing import List, Dict, Optional
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.ai_model import get_ai_model
from database.db_manager import db_manager
from utils.image_processor import image_processor
from utils.scoring import scoring_engine

def render_tab2():
    """渲染Tab2 - 学生作业批改页面"""
    
    st.header("📊 学生作业批改")
    
    # 检查是否有标准答案
    standard_answers = db_manager.get_standard_answers()
    if not standard_answers:
        st.error("❌ 请先在「标准答案设置」页面设置标准答案")
        return
    
    st.success(f"✅ 已加载 {len(standard_answers)} 道题的标准答案")
    
    # 侧边栏 - 批改历史
    with st.sidebar:
        st.subheader("📚 批改历史")
        display_grading_history()
    
    # 主要内容区域
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.subheader("1. 上传学生作业")
        
        uploaded_file = st.file_uploader(
            "选择学生作业图片",
            type=['jpg', 'jpeg', 'png'],
            help="支持JPG、PNG格式，建议图片清晰度高，文字清楚"
        )
        
        if uploaded_file is not None:
            # 验证图片
            if image_processor.validate_image(uploaded_file):
                # 显示图片预览
                image = Image.open(uploaded_file)
                st.image(image, caption="学生作业预览", use_column_width=True)
                
                # 保存图片
                if 'student_image_path' not in st.session_state:
                    image_path = image_processor.save_uploaded_image(uploaded_file, "student")
                    if image_path:
                        st.session_state.student_image_path = image_path
                        st.success("✅ 图片上传成功！")
                    else:
                        st.error("❌ 图片保存失败")
                
                # AI识别按钮
                if st.button("🤖 AI识别并评分", type="primary", use_container_width=True):
                    grade_student_work(standard_answers)
            else:
                st.error("❌ 无效的图片文件，请上传JPG或PNG格式的图片")
    
    with col2:
        st.subheader("2. AI识别结果")
        
        # 显示识别进度
        if 'grading' in st.session_state and st.session_state.grading:
            with st.spinner("🤖 AI正在识别和评分，请稍候..."):
                st.info("正在分析学生作业，识别答案并进行评分...")
        
        # 显示学生信息
        if 'student_info' in st.session_state:
            display_student_info()
    
    # 评分结果编辑区域
    st.markdown("---")
    st.subheader("3. 评分结果")
    
    if 'grading_results' in st.session_state:
        edit_grading_results()
    
    # 保存按钮
    if 'grading_results' in st.session_state and st.session_state.grading_results:
        st.markdown("---")
        col1, col2, col3 = st.columns([1, 1, 1])
        
        with col2:
            if st.button("💾 保存评分结果", type="primary", use_container_width=True):
                save_grading_results()

def grade_student_work(standard_answers: List[Dict]):
    """AI识别并评分学生作业"""
    if 'student_image_path' not in st.session_state:
        st.error("请先上传学生作业图片")
        return
    
    try:
        st.session_state.grading = True
        st.rerun()
        
        # 加载AI模型
        ai_model = get_ai_model()
        
        # 加载图片
        image = image_processor.load_image(st.session_state.student_image_path)
        if image is None:
            st.error("无法加载图片")
            return
        
        # 预处理图片
        processed_image = image_processor.preprocess_image(image)
        
        # AI识别学生答案
        extraction_result = ai_model.extract_student_answers(processed_image, standard_answers)
        
        if not extraction_result:
            st.error("❌ AI识别失败")
            return
        
        # 保存学生信息
        st.session_state.student_info = extraction_result.get('student_info', {})
        student_answers = extraction_result.get('answers', [])
        
        if not student_answers:
            st.warning("⚠️ 未能识别出学生答案")
            return
        
        # 进行评分
        grading_results = []
        total_score = 0
        max_score = 0
        
        for standard in standard_answers:
            question_no = standard['question_no']
            
            # 找到对应的学生答案
            student_answer = ""
            confidence = 0.0
            
            for sa in student_answers:
                if sa.get('question_no') == question_no:
                    student_answer = sa.get('content', '')
                    confidence = sa.get('confidence', 0.0)
                    break
            
            # 评分
            score_result = scoring_engine.score_answer(student_answer, standard)
            
            # 组合结果
            result = {
                'question_no': question_no,
                'student_answer': student_answer,
                'standard_answer': standard['content'],
                'score_earned': score_result['score_earned'],
                'score_total': score_result['score_total'],
                'is_correct': score_result['is_correct'],
                'grading_type': score_result['grading_type'],
                'grading_reason': score_result['grading_reason'],
                'confidence': confidence
            }
            
            grading_results.append(result)
            total_score += score_result['score_earned']
            max_score += score_result['score_total']
        
        # 保存评分结果
        st.session_state.grading_results = grading_results
        st.session_state.total_score = total_score
        st.session_state.max_score = max_score
        st.session_state.accuracy = (total_score / max_score * 100) if max_score > 0 else 0
        
        st.success(f"✅ 评分完成！总分: {total_score:.1f}/{max_score:.1f} ({st.session_state.accuracy:.1f}%)")
        
    except Exception as e:
        st.error(f"❌ 评分失败: {str(e)}")
    
    finally:
        st.session_state.grading = False
        st.rerun()

def display_student_info():
    """显示学生信息"""
    student_info = st.session_state.student_info
    
    col1, col2, col3 = st.columns([1, 1, 1])
    
    with col1:
        st.metric("学生姓名", student_info.get('name', '未识别') or '未识别')
    
    with col2:
        st.metric("学号", student_info.get('student_id', '未识别') or '未识别')
    
    with col3:
        if 'accuracy' in st.session_state:
            st.metric("正确率", f"{st.session_state.accuracy:.1f}%")

def edit_grading_results():
    """编辑评分结果"""
    results = st.session_state.grading_results
    
    if not results:
        st.info("暂无评分结果")
        return
    
    # 总分显示
    total_score = st.session_state.get('total_score', 0)
    max_score = st.session_state.get('max_score', 0)
    accuracy = st.session_state.get('accuracy', 0)
    
    st.info(f"📊 总分: **{total_score:.1f}/{max_score:.1f}** | 正确率: **{accuracy:.1f}%**")
    
    # 逐题显示和编辑
    for i, result in enumerate(results):
        question_no = result['question_no']
        
        with st.expander(
            f"📝 第 {question_no} 题 - {result['score_earned']:.1f}/{result['score_total']:.1f}分 "
            f"{'✅' if result['is_correct'] else '❌'}",
            expanded=not result['is_correct']  # 错误的题目默认展开
        ):
            
            col1, col2 = st.columns([2, 1])
            
            with col1:
                # 学生答案
                st.text_area(
                    "学生答案",
                    value=result['student_answer'],
                    height=80,
                    disabled=True,
                    key=f"student_ans_{i}"
                )
                
                # 标准答案
                st.text_area(
                    "标准答案",
                    value=result['standard_answer'],
                    height=80,
                    disabled=True,
                    key=f"standard_ans_{i}"
                )
                
                # 评分说明
                st.text_input(
                    "评分说明",
                    value=result['grading_reason'],
                    disabled=True,
                    key=f"reason_{i}"
                )
            
            with col2:
                # 当前得分
                st.metric("当前得分", f"{result['score_earned']:.1f}")
                st.metric("满分", f"{result['score_total']:.1f}")
                st.metric("AI置信度", f"{result['confidence']:.1%}")
                
                # 手动调整分数
                st.markdown("**手动调整:**")
                
                new_score = st.number_input(
                    "调整得分",
                    min_value=0.0,
                    max_value=float(result['score_total']),
                    value=float(result['score_earned']),
                    step=0.1,
                    key=f"adjust_score_{i}"
                )
                
                adjust_reason = st.text_area(
                    "调整原因",
                    height=60,
                    key=f"adjust_reason_{i}",
                    placeholder="请说明调整原因..."
                )
                
                if st.button(f"✏️ 应用调整", key=f"apply_{i}"):
                    apply_score_adjustment(i, new_score, adjust_reason)

def apply_score_adjustment(index: int, new_score: float, reason: str):
    """应用分数调整"""
    try:
        # 更新评分结果
        st.session_state.grading_results[index]['score_earned'] = new_score
        st.session_state.grading_results[index]['grading_reason'] = reason or "手动调整"
        st.session_state.grading_results[index]['is_correct'] = new_score > 0
        
        # 重新计算总分
        total_score = sum(r['score_earned'] for r in st.session_state.grading_results)
        max_score = sum(r['score_total'] for r in st.session_state.grading_results)
        accuracy = (total_score / max_score * 100) if max_score > 0 else 0
        
        st.session_state.total_score = total_score
        st.session_state.max_score = max_score
        st.session_state.accuracy = accuracy
        
        st.success("✅ 分数调整成功！")
        st.rerun()
        
    except Exception as e:
        st.error(f"❌ 调整失败: {str(e)}")

def save_grading_results():
    """保存评分结果"""
    try:
        student_info = st.session_state.get('student_info', {})
        grading_results = st.session_state.get('grading_results', [])
        student_image_path = st.session_state.get('student_image_path', '')
        
        if not grading_results:
            st.error("没有评分结果可保存")
            return
        
        # 保存到数据库
        submission_id = db_manager.save_student_submission(
            student_name=student_info.get('name', ''),
            student_id=student_info.get('student_id', ''),
            image_path=student_image_path,
            answers=grading_results
        )
        
        if submission_id:
            st.success("✅ 评分结果保存成功！")
            st.balloons()
            
            # 清空当前状态
            clear_current_session()
        else:
            st.error("❌ 保存失败，请重试")
    
    except Exception as e:
        st.error(f"❌ 保存失败: {str(e)}")

def clear_current_session():
    """清空当前会话状态"""
    keys_to_clear = [
        'student_image_path', 'student_info', 'grading_results',
        'total_score', 'max_score', 'accuracy'
    ]
    
    for key in keys_to_clear:
        if key in st.session_state:
            del st.session_state[key]

def display_grading_history():
    """显示批改历史"""
    try:
        submissions = db_manager.get_all_submissions()
        
        if not submissions:
            st.info("暂无批改历史")
            return
        
        st.write(f"共 {len(submissions)} 份作业")
        
        # 显示最近的几份作业
        for submission in submissions[:5]:
            with st.expander(
                f"{submission.get('student_name', '未知')} - {submission.get('total_score', 0):.1f}分",
                expanded=False
            ):
                st.write(f"**学号:** {submission.get('student_id', '未知')}")
                st.write(f"**总分:** {submission.get('total_score', 0):.1f}/{submission.get('max_score', 0):.1f}")
                st.write(f"**正确率:** {submission.get('accuracy', 0):.1f}%")
                st.write(f"**提交时间:** {submission.get('submitted_at', '')}")
        
        if len(submissions) > 5:
            st.write(f"... 还有 {len(submissions) - 5} 份作业")
    
    except Exception as e:
        st.error(f"❌ 加载历史失败: {str(e)}")
