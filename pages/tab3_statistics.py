import streamlit as st
import pandas as pd
import matplotlib.pyplot as plt
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np
from typing import List, Dict
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_manager import db_manager
from utils.export import data_exporter

def render_tab3():
    """渲染Tab3 - 成绩统计分析页面"""
    
    st.header("📈 成绩统计分析")
    
    # 加载数据
    submissions = db_manager.get_all_submissions()
    
    if not submissions:
        st.info("📝 暂无学生作业数据，请先在「学生作业批改」页面批改作业")
        return
    
    # 侧边栏 - 操作选项
    with st.sidebar:
        st.subheader("📊 数据操作")
        
        # 导出选项
        st.markdown("**导出数据:**")
        
        if st.button("📥 导出成绩汇总"):
            export_summary_data(submissions)
        
        if st.button("📥 导出详细评分"):
            export_detailed_data(submissions)
        
        if st.button("📥 导出统计报告"):
            export_statistics_report(submissions)
        
        st.markdown("---")
        
        # 数据管理
        st.markdown("**数据管理:**")
        
        if st.button("🗑️ 清空所有数据", type="secondary"):
            clear_all_data()
        
        st.markdown("---")
        
        # 筛选选项
        st.markdown("**数据筛选:**")
        
        # 按分数范围筛选
        score_range = st.slider(
            "分数范围",
            min_value=0.0,
            max_value=100.0,
            value=(0.0, 100.0),
            step=1.0
        )
        
        # 按正确率筛选
        accuracy_range = st.slider(
            "正确率范围 (%)",
            min_value=0.0,
            max_value=100.0,
            value=(0.0, 100.0),
            step=1.0
        )
    
    # 应用筛选
    filtered_submissions = filter_submissions(submissions, score_range, accuracy_range)
    
    if not filtered_submissions:
        st.warning("⚠️ 筛选条件下没有数据")
        return
    
    # 主要内容区域
    display_overview_metrics(filtered_submissions)
    
    st.markdown("---")
    
    # 数据可视化
    col1, col2 = st.columns([1, 1])
    
    with col1:
        display_score_distribution(filtered_submissions)
        display_accuracy_chart(filtered_submissions)
    
    with col2:
        display_student_ranking(filtered_submissions)
        display_question_analysis(filtered_submissions)
    
    st.markdown("---")
    
    # 详细数据表
    display_detailed_table(filtered_submissions)

def filter_submissions(submissions: List[Dict], score_range: tuple, accuracy_range: tuple) -> List[Dict]:
    """筛选提交数据"""
    filtered = []
    
    for submission in submissions:
        total_score = submission.get('total_score', 0)
        max_score = submission.get('max_score', 1)
        accuracy = submission.get('accuracy', 0)
        
        # 计算百分制分数
        percentage_score = (total_score / max_score * 100) if max_score > 0 else 0
        
        # 应用筛选条件
        if (score_range[0] <= percentage_score <= score_range[1] and
            accuracy_range[0] <= accuracy <= accuracy_range[1]):
            filtered.append(submission)
    
    return filtered

def display_overview_metrics(submissions: List[Dict]):
    """显示概览指标"""
    st.subheader("📊 数据概览")
    
    # 计算统计指标
    total_students = len(submissions)
    
    scores = []
    accuracies = []
    
    for submission in submissions:
        total_score = submission.get('total_score', 0)
        max_score = submission.get('max_score', 1)
        accuracy = submission.get('accuracy', 0)
        
        percentage_score = (total_score / max_score * 100) if max_score > 0 else 0
        scores.append(percentage_score)
        accuracies.append(accuracy)
    
    if scores:
        avg_score = np.mean(scores)
        max_score = np.max(scores)
        min_score = np.min(scores)
        pass_rate = len([s for s in scores if s >= 60]) / len(scores) * 100
        excellent_rate = len([s for s in scores if s >= 90]) / len(scores) * 100
    else:
        avg_score = max_score = min_score = pass_rate = excellent_rate = 0
    
    # 显示指标
    col1, col2, col3, col4, col5 = st.columns(5)
    
    with col1:
        st.metric("总学生数", total_students)
    
    with col2:
        st.metric("平均分", f"{avg_score:.1f}")
    
    with col3:
        st.metric("最高分", f"{max_score:.1f}")
    
    with col4:
        st.metric("及格率", f"{pass_rate:.1f}%")
    
    with col5:
        st.metric("优秀率", f"{excellent_rate:.1f}%")

def display_score_distribution(submissions: List[Dict]):
    """显示分数分布图"""
    st.subheader("📊 分数分布")
    
    # 准备数据
    scores = []
    names = []
    
    for submission in submissions:
        total_score = submission.get('total_score', 0)
        max_score = submission.get('max_score', 1)
        percentage_score = (total_score / max_score * 100) if max_score > 0 else 0
        
        scores.append(percentage_score)
        names.append(submission.get('student_name', '未知'))
    
    if not scores:
        st.info("暂无数据")
        return
    
    # 创建直方图
    fig = px.histogram(
        x=scores,
        nbins=10,
        title="分数分布直方图",
        labels={'x': '分数', 'y': '学生人数'},
        color_discrete_sequence=['#1f77b4']
    )
    
    fig.update_layout(
        xaxis_title="分数",
        yaxis_title="学生人数",
        showlegend=False
    )
    
    st.plotly_chart(fig, use_container_width=True)

def display_accuracy_chart(submissions: List[Dict]):
    """显示正确率图表"""
    st.subheader("🎯 正确率分析")
    
    # 准备数据
    data = []
    
    for submission in submissions:
        data.append({
            'student_name': submission.get('student_name', '未知'),
            'accuracy': submission.get('accuracy', 0),
            'total_score': submission.get('total_score', 0),
            'max_score': submission.get('max_score', 1)
        })
    
    if not data:
        st.info("暂无数据")
        return
    
    df = pd.DataFrame(data)
    
    # 创建散点图
    fig = px.scatter(
        df,
        x='student_name',
        y='accuracy',
        size='total_score',
        title="学生正确率分布",
        labels={'student_name': '学生姓名', 'accuracy': '正确率 (%)'},
        color='accuracy',
        color_continuous_scale='RdYlGn'
    )
    
    fig.update_layout(
        xaxis_title="学生姓名",
        yaxis_title="正确率 (%)",
        xaxis={'tickangle': 45}
    )
    
    st.plotly_chart(fig, use_container_width=True)

def display_student_ranking(submissions: List[Dict]):
    """显示学生排名"""
    st.subheader("🏆 学生排名")
    
    # 准备排名数据
    ranking_data = []
    
    for submission in submissions:
        total_score = submission.get('total_score', 0)
        max_score = submission.get('max_score', 1)
        percentage_score = (total_score / max_score * 100) if max_score > 0 else 0
        
        ranking_data.append({
            'student_name': submission.get('student_name', '未知'),
            'student_id': submission.get('student_id', ''),
            'percentage_score': percentage_score,
            'accuracy': submission.get('accuracy', 0),
            'total_score': total_score,
            'max_score': max_score
        })
    
    # 按分数排序
    ranking_data.sort(key=lambda x: x['percentage_score'], reverse=True)
    
    # 显示前10名
    top_students = ranking_data[:10]
    
    if top_students:
        df = pd.DataFrame(top_students)
        df.index = range(1, len(df) + 1)  # 排名从1开始
        
        # 格式化显示
        display_df = df[['student_name', 'percentage_score', 'accuracy']].copy()
        display_df.columns = ['学生姓名', '分数', '正确率(%)']
        display_df['分数'] = display_df['分数'].round(1)
        display_df['正确率(%)'] = display_df['正确率(%)'].round(1)
        
        st.dataframe(display_df, use_container_width=True)
    else:
        st.info("暂无排名数据")

def display_question_analysis(submissions: List[Dict]):
    """显示题目分析"""
    st.subheader("📝 题目分析")
    
    # 收集所有评分详情
    all_details = []
    
    for submission in submissions:
        submission_id = submission['id']
        details = db_manager.get_submission_details(submission_id)
        all_details.extend(details)
    
    if not all_details:
        st.info("暂无题目分析数据")
        return
    
    # 按题目统计
    question_stats = {}
    
    for detail in all_details:
        question_no = detail['question_no']
        
        if question_no not in question_stats:
            question_stats[question_no] = {
                'total_students': 0,
                'correct_count': 0,
                'total_score': 0,
                'max_score': 0
            }
        
        stats = question_stats[question_no]
        stats['total_students'] += 1
        
        if detail.get('is_correct', False):
            stats['correct_count'] += 1
        
        stats['total_score'] += detail.get('score_earned', 0)
        stats['max_score'] += detail.get('score_total', 1)
    
    # 计算正确率
    question_data = []
    
    for question_no, stats in question_stats.items():
        correct_rate = (stats['correct_count'] / stats['total_students'] * 100) if stats['total_students'] > 0 else 0
        avg_score = (stats['total_score'] / stats['max_score'] * 100) if stats['max_score'] > 0 else 0
        
        question_data.append({
            'question_no': f"第{question_no}题",
            'correct_rate': correct_rate,
            'avg_score': avg_score,
            'total_students': stats['total_students']
        })
    
    if question_data:
        df = pd.DataFrame(question_data)
        
        # 创建柱状图
        fig = px.bar(
            df,
            x='question_no',
            y='correct_rate',
            title="各题正确率",
            labels={'question_no': '题目', 'correct_rate': '正确率 (%)'},
            color='correct_rate',
            color_continuous_scale='RdYlGn'
        )
        
        fig.update_layout(
            xaxis_title="题目",
            yaxis_title="正确率 (%)"
        )
        
        st.plotly_chart(fig, use_container_width=True)

def display_detailed_table(submissions: List[Dict]):
    """显示详细数据表"""
    st.subheader("📋 详细数据表")
    
    # 准备表格数据
    table_data = []
    
    for submission in submissions:
        total_score = submission.get('total_score', 0)
        max_score = submission.get('max_score', 1)
        percentage_score = (total_score / max_score * 100) if max_score > 0 else 0
        
        table_data.append({
            '学生姓名': submission.get('student_name', '未知'),
            '学号': submission.get('student_id', ''),
            '总分': f"{total_score:.1f}/{max_score:.1f}",
            '百分制': f"{percentage_score:.1f}",
            '正确率(%)': f"{submission.get('accuracy', 0):.1f}",
            '提交时间': submission.get('submitted_at', ''),
            '状态': submission.get('status', 'unknown')
        })
    
    if table_data:
        df = pd.DataFrame(table_data)
        st.dataframe(df, use_container_width=True)
    else:
        st.info("暂无详细数据")

def export_summary_data(submissions: List[Dict]):
    """导出成绩汇总数据"""
    try:
        # 收集详细信息
        details_dict = {}
        
        for submission in submissions:
            submission_id = submission['id']
            details = db_manager.get_submission_details(submission_id)
            details_dict[submission_id] = details
        
        # 导出
        csv_data = data_exporter.export_grades_summary(submissions, details_dict)
        data_exporter.create_download_link(csv_data, "成绩汇总")
        
        st.success("✅ 成绩汇总导出文件已准备就绪")
    
    except Exception as e:
        st.error(f"❌ 导出失败: {str(e)}")

def export_detailed_data(submissions: List[Dict]):
    """导出详细评分数据"""
    try:
        # 收集详细信息
        details_dict = {}
        
        for submission in submissions:
            submission_id = submission['id']
            details = db_manager.get_submission_details(submission_id)
            details_dict[submission_id] = details
        
        # 导出
        csv_data = data_exporter.export_detailed_grades(submissions, details_dict)
        data_exporter.create_download_link(csv_data, "详细评分")
        
        st.success("✅ 详细评分导出文件已准备就绪")
    
    except Exception as e:
        st.error(f"❌ 导出失败: {str(e)}")

def export_statistics_report(submissions: List[Dict]):
    """导出统计报告"""
    try:
        # 计算统计数据
        scores = []
        
        for submission in submissions:
            total_score = submission.get('total_score', 0)
            max_score = submission.get('max_score', 1)
            percentage_score = (total_score / max_score * 100) if max_score > 0 else 0
            scores.append(percentage_score)
        
        stats_data = {
            'total_students': len(submissions),
            'average_score': np.mean(scores) if scores else 0,
            'max_score': np.max(scores) if scores else 0,
            'min_score': np.min(scores) if scores else 0,
            'pass_rate': len([s for s in scores if s >= 60]) / len(scores) * 100 if scores else 0,
            'excellent_rate': len([s for s in scores if s >= 90]) / len(scores) * 100 if scores else 0
        }
        
        # 导出
        csv_data = data_exporter.export_statistics(stats_data)
        data_exporter.create_download_link(csv_data, "统计报告")
        
        st.success("✅ 统计报告导出文件已准备就绪")
    
    except Exception as e:
        st.error(f"❌ 导出失败: {str(e)}")

def clear_all_data():
    """清空所有数据"""
    if st.button("⚠️ 确认清空所有数据", type="secondary"):
        try:
            success = db_manager.clear_all_data()
            
            if success:
                st.success("✅ 所有数据已清空")
                st.rerun()
            else:
                st.error("❌ 清空失败")
        
        except Exception as e:
            st.error(f"❌ 清空失败: {str(e)}")
