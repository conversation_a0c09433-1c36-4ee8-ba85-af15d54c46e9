import os
import json
import re
from PIL import Image
from transformers import AutoTokenizer
from vllm import LLM, SamplingParams
from typing import Dict, List, Optional, Tuple
import streamlit as st

class HomeworkGraderAI:
    def __init__(self, model_name: str = "openbmb/MiniCPM-V-4-AWQ"):
        self.model_name = model_name
        self.tokenizer = None
        self.llm = None
        self.sampling_params = None
        self._initialize_model()
    
    def _initialize_model(self):
        """初始化AI模型"""
        try:
            # 初始化tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_name, 
                trust_remote_code=True
            )
            
            # 初始化LLM
            self.llm = LLM(
                model=self.model_name,
                max_model_len=2048,
                trust_remote_code=True,
            )
            
            # 设置生成参数
            stop_tokens = ['<|im_end|>', '</s>']
            stop_token_ids = [self.tokenizer.convert_tokens_to_ids(i) for i in stop_tokens]
            
            self.sampling_params = SamplingParams(
                stop_token_ids=stop_token_ids,
                temperature=0.3,  # 降低温度提高一致性
                top_p=0.8,
                max_tokens=1024,
            )
            
            print("AI模型初始化成功")
            
        except Exception as e:
            print(f"AI模型初始化失败: {e}")
            raise e
    
    def _generate_response(self, image: Image.Image, prompt: str) -> str:
        """生成AI响应"""
        try:
            # 构建消息
            messages = [{
                "role": "user",
                "content": f"(<image>./</image>)\n{prompt}",
            }]
            
            # 应用聊天模板
            formatted_prompt = self.tokenizer.apply_chat_template(
                messages, 
                tokenize=False, 
                add_generation_prompt=True
            )
            
            # 生成响应
            outputs = self.llm.generate({
                "prompt": formatted_prompt,
                "multi_modal_data": {
                    "image": image
                }
            }, self.sampling_params)
            
            return outputs[0].outputs[0].text.strip()
            
        except Exception as e:
            print(f"生成AI响应失败: {e}")
            return ""
    
    def extract_standard_answers(self, image: Image.Image) -> List[Dict]:
        """从答案图片中提取标准答案"""
        prompt = """请仔细分析这张答案图片，提取出所有题目的标准答案。

要求：
1. 识别每道题的序号和对应答案
2. 提取答案中的关键词
3. 返回严格的JSON格式

返回格式：
{
  "answers": [
    {
      "question_no": 1,
      "content": "完整答案内容",
      "keywords": ["关键词1", "关键词2", "关键词3"]
    },
    {
      "question_no": 2,
      "content": "完整答案内容",
      "keywords": ["关键词1", "关键词2"]
    }
  ]
}

注意：只返回JSON，不要添加其他文字说明。"""

        response = self._generate_response(image, prompt)
        
        try:
            # 尝试解析JSON
            # 清理响应文本，提取JSON部分
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                data = json.loads(json_str)
                
                # 验证数据格式
                if 'answers' in data and isinstance(data['answers'], list):
                    answers = []
                    for i, answer in enumerate(data['answers'], 1):
                        answers.append({
                            'question_no': answer.get('question_no', i),
                            'content': answer.get('content', ''),
                            'keywords': answer.get('keywords', []),
                            'score': 1.0,  # 默认分值
                            'grading_type': 'exact'  # 默认评分类型
                        })
                    return answers
                    
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            print(f"原始响应: {response}")
        
        # 如果JSON解析失败，尝试文本解析
        return self._parse_answers_from_text(response)
    
    def extract_student_answers(self, image: Image.Image, standard_answers: List[Dict]) -> Dict:
        """从学生作业图片中提取答案和学生信息"""
        
        # 构建标准答案参考
        standard_ref = "\n".join([
            f"题目{ans['question_no']}: {ans['content']}" 
            for ans in standard_answers
        ])
        
        prompt = f"""请分析这张学生作业图片，识别学生信息和答案。

标准答案参考：
{standard_ref}

要求：
1. 识别学生姓名和学号（如果有）
2. 识别每道题的学生答案
3. 返回严格的JSON格式

返回格式：
{{
  "student_info": {{
    "name": "学生姓名",
    "student_id": "学号"
  }},
  "answers": [
    {{
      "question_no": 1,
      "content": "学生答案内容",
      "confidence": 0.95
    }},
    {{
      "question_no": 2,
      "content": "学生答案内容", 
      "confidence": 0.90
    }}
  ]
}}

注意：只返回JSON，不要添加其他文字说明。如果无法识别学生信息，name和student_id设为空字符串。"""

        response = self._generate_response(image, prompt)
        
        try:
            # 解析JSON响应
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                data = json.loads(json_str)
                
                # 验证和标准化数据
                result = {
                    'student_info': {
                        'name': data.get('student_info', {}).get('name', ''),
                        'student_id': data.get('student_info', {}).get('student_id', '')
                    },
                    'answers': []
                }
                
                if 'answers' in data:
                    for answer in data['answers']:
                        result['answers'].append({
                            'question_no': answer.get('question_no', 1),
                            'content': answer.get('content', ''),
                            'confidence': answer.get('confidence', 0.0)
                        })
                
                return result
                
        except json.JSONDecodeError as e:
            print(f"学生答案JSON解析失败: {e}")
            print(f"原始响应: {response}")
        
        # 返回空结果
        return {
            'student_info': {'name': '', 'student_id': ''},
            'answers': []
        }
    
    def _parse_answers_from_text(self, text: str) -> List[Dict]:
        """从文本中解析答案（备用方法）"""
        answers = []
        lines = text.split('\n')
        
        current_question = 1
        current_content = ""
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 检查是否是题号
            question_match = re.match(r'(\d+)[\.、:]?\s*(.*)', line)
            if question_match:
                # 保存前一题
                if current_content:
                    answers.append({
                        'question_no': current_question,
                        'content': current_content.strip(),
                        'keywords': self._extract_keywords(current_content),
                        'score': 1.0,
                        'grading_type': 'exact'
                    })
                
                # 开始新题
                current_question = int(question_match.group(1))
                current_content = question_match.group(2)
            else:
                # 继续当前题的内容
                current_content += " " + line
        
        # 保存最后一题
        if current_content:
            answers.append({
                'question_no': current_question,
                'content': current_content.strip(),
                'keywords': self._extract_keywords(current_content),
                'score': 1.0,
                'grading_type': 'exact'
            })
        
        return answers
    
    def _extract_keywords(self, text: str) -> List[str]:
        """从文本中提取关键词"""
        # 简单的关键词提取逻辑
        # 移除标点符号，分割单词
        import string
        text = text.translate(str.maketrans('', '', string.punctuation))
        words = text.split()
        
        # 过滤短词和常见词
        stop_words = {'的', '是', '在', '有', '和', '与', '或', '但', '因为', '所以', '如果', '那么'}
        keywords = [word for word in words if len(word) > 1 and word not in stop_words]
        
        return keywords[:5]  # 返回前5个关键词

# 全局AI模型实例
@st.cache_resource
def get_ai_model():
    """获取AI模型实例（使用Streamlit缓存）"""
    return HomeworkGraderAI()
