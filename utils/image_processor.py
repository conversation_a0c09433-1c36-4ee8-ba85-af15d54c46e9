import os
import hashlib
from PIL import Image, ImageOps
from typing import Optional, Tuple
import streamlit as st

class ImageProcessor:
    """图像处理工具类"""
    
    def __init__(self, upload_dir: str = "uploads"):
        self.upload_dir = upload_dir
        self.ensure_upload_dir()
    
    def ensure_upload_dir(self):
        """确保上传目录存在"""
        if not os.path.exists(self.upload_dir):
            os.makedirs(self.upload_dir)
    
    def save_uploaded_image(self, uploaded_file, prefix: str = "img") -> Optional[str]:
        """
        保存上传的图片文件
        
        Args:
            uploaded_file: Streamlit上传的文件对象
            prefix: 文件名前缀
            
        Returns:
            保存的文件路径，失败返回None
        """
        try:
            # 生成唯一文件名
            file_hash = hashlib.md5(uploaded_file.getvalue()).hexdigest()[:8]
            file_extension = uploaded_file.name.split('.')[-1].lower()
            filename = f"{prefix}_{file_hash}.{file_extension}"
            filepath = os.path.join(self.upload_dir, filename)
            
            # 保存文件
            with open(filepath, "wb") as f:
                f.write(uploaded_file.getbuffer())
            
            return filepath
            
        except Exception as e:
            st.error(f"保存图片失败: {e}")
            return None
    
    def load_image(self, filepath: str) -> Optional[Image.Image]:
        """
        加载图片文件
        
        Args:
            filepath: 图片文件路径
            
        Returns:
            PIL Image对象，失败返回None
        """
        try:
            if not os.path.exists(filepath):
                return None
            
            image = Image.open(filepath)
            # 转换为RGB格式
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            return image
            
        except Exception as e:
            print(f"加载图片失败: {e}")
            return None
    
    def preprocess_image(self, image: Image.Image, max_size: Tuple[int, int] = (1024, 1024)) -> Image.Image:
        """
        预处理图片
        
        Args:
            image: 原始图片
            max_size: 最大尺寸
            
        Returns:
            处理后的图片
        """
        try:
            # 自动旋转（基于EXIF信息）
            image = ImageOps.exif_transpose(image)
            
            # 调整大小（保持宽高比）
            image.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # 确保是RGB格式
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            return image
            
        except Exception as e:
            print(f"图片预处理失败: {e}")
            return image
    
    def validate_image(self, uploaded_file) -> bool:
        """
        验证上传的图片文件
        
        Args:
            uploaded_file: Streamlit上传的文件对象
            
        Returns:
            是否为有效图片
        """
        try:
            # 检查文件扩展名
            if uploaded_file.name:
                ext = uploaded_file.name.split('.')[-1].lower()
                if ext not in ['jpg', 'jpeg', 'png']:
                    return False
            
            # 检查文件大小（限制为10MB）
            if uploaded_file.size > 10 * 1024 * 1024:
                return False
            
            # 尝试打开图片
            image = Image.open(uploaded_file)
            image.verify()  # 验证图片完整性
            
            return True
            
        except Exception:
            return False
    
    def get_image_info(self, image: Image.Image) -> dict:
        """
        获取图片信息
        
        Args:
            image: PIL Image对象
            
        Returns:
            图片信息字典
        """
        return {
            'size': image.size,
            'mode': image.mode,
            'format': image.format,
            'width': image.width,
            'height': image.height
        }
    
    def cleanup_old_files(self, days: int = 7):
        """
        清理旧文件
        
        Args:
            days: 保留天数
        """
        try:
            import time
            current_time = time.time()
            cutoff_time = current_time - (days * 24 * 60 * 60)
            
            for filename in os.listdir(self.upload_dir):
                filepath = os.path.join(self.upload_dir, filename)
                if os.path.isfile(filepath):
                    file_time = os.path.getmtime(filepath)
                    if file_time < cutoff_time:
                        os.remove(filepath)
                        print(f"删除旧文件: {filename}")
                        
        except Exception as e:
            print(f"清理文件失败: {e}")

# 全局图像处理器实例
image_processor = ImageProcessor()
