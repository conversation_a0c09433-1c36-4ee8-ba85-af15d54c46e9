import pandas as pd
import csv
import json
from datetime import datetime
from typing import List, Dict
import streamlit as st
from io import StringIO, BytesIO

class DataExporter:
    """数据导出工具类"""
    
    def __init__(self):
        pass
    
    def export_to_csv(self, data: List[Dict], filename: str = None) -> str:
        """
        导出数据到CSV格式
        
        Args:
            data: 要导出的数据列表
            filename: 文件名（可选）
            
        Returns:
            CSV字符串
        """
        if not data:
            return ""
        
        # 创建DataFrame
        df = pd.DataFrame(data)
        
        # 转换为CSV字符串
        csv_buffer = StringIO()
        df.to_csv(csv_buffer, index=False, encoding='utf-8-sig')
        csv_string = csv_buffer.getvalue()
        
        return csv_string
    
    def export_grades_summary(self, submissions: List[Dict], details: Dict[int, List[Dict]]) -> str:
        """
        导出成绩汇总表
        
        Args:
            submissions: 学生提交记录
            details: 评分详情 {submission_id: [details]}
            
        Returns:
            CSV字符串
        """
        export_data = []
        
        for submission in submissions:
            submission_id = submission['id']
            student_details = details.get(submission_id, [])
            
            # 基础信息
            row = {
                '学生姓名': submission.get('student_name', ''),
                '学号': submission.get('student_id', ''),
                '总分': submission.get('total_score', 0),
                '满分': submission.get('max_score', 0),
                '正确率(%)': round(submission.get('accuracy', 0), 2),
                '提交时间': submission.get('submitted_at', ''),
                '评分时间': submission.get('graded_at', '')
            }
            
            # 添加各题得分
            for detail in student_details:
                question_no = detail['question_no']
                score_earned = detail['score_earned']
                score_total = detail['score_total']
                row[f'第{question_no}题'] = f"{score_earned}/{score_total}"
                row[f'第{question_no}题_学生答案'] = detail.get('student_answer', '')
                row[f'第{question_no}题_评分说明'] = detail.get('grading_reason', '')
            
            export_data.append(row)
        
        return self.export_to_csv(export_data)
    
    def export_detailed_grades(self, submissions: List[Dict], details: Dict[int, List[Dict]]) -> str:
        """
        导出详细评分表
        
        Args:
            submissions: 学生提交记录
            details: 评分详情
            
        Returns:
            CSV字符串
        """
        export_data = []
        
        for submission in submissions:
            submission_id = submission['id']
            student_details = details.get(submission_id, [])
            
            for detail in student_details:
                row = {
                    '学生姓名': submission.get('student_name', ''),
                    '学号': submission.get('student_id', ''),
                    '题号': detail['question_no'],
                    '学生答案': detail.get('student_answer', ''),
                    '标准答案': detail.get('standard_answer', ''),
                    '得分': detail['score_earned'],
                    '满分': detail['score_total'],
                    '评分类型': detail.get('grading_type', ''),
                    '评分说明': detail.get('grading_reason', ''),
                    '是否正确': '是' if detail.get('is_correct', False) else '否',
                    '置信度': detail.get('confidence', 0),
                    '手动调整': '是' if detail.get('manual_adjusted', False) else '否',
                    '提交时间': submission.get('submitted_at', '')
                }
                export_data.append(row)
        
        return self.export_to_csv(export_data)
    
    def export_statistics(self, stats_data: Dict) -> str:
        """
        导出统计数据
        
        Args:
            stats_data: 统计数据字典
            
        Returns:
            CSV字符串
        """
        export_data = []
        
        # 基础统计
        basic_stats = {
            '统计项目': ['总学生数', '平均分', '最高分', '最低分', '及格率(%)', '优秀率(%)'],
            '数值': [
                stats_data.get('total_students', 0),
                round(stats_data.get('average_score', 0), 2),
                stats_data.get('max_score', 0),
                stats_data.get('min_score', 0),
                round(stats_data.get('pass_rate', 0), 2),
                round(stats_data.get('excellent_rate', 0), 2)
            ]
        }
        
        df_basic = pd.DataFrame(basic_stats)
        csv_basic = df_basic.to_csv(index=False, encoding='utf-8-sig')
        
        # 分数分布
        if 'score_distribution' in stats_data:
            csv_basic += "\n\n分数分布:\n"
            df_dist = pd.DataFrame(stats_data['score_distribution'])
            csv_dist = df_dist.to_csv(index=False, encoding='utf-8-sig')
            csv_basic += csv_dist
        
        return csv_basic
    
    def create_download_link(self, csv_string: str, filename: str) -> None:
        """
        创建下载链接
        
        Args:
            csv_string: CSV字符串
            filename: 文件名
        """
        # 添加时间戳到文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        full_filename = f"{filename}_{timestamp}.csv"
        
        # 创建下载按钮
        st.download_button(
            label=f"📥 下载 {filename}",
            data=csv_string.encode('utf-8-sig'),
            file_name=full_filename,
            mime='text/csv'
        )
    
    def export_standard_answers(self, answers: List[Dict]) -> str:
        """
        导出标准答案
        
        Args:
            answers: 标准答案列表
            
        Returns:
            CSV字符串
        """
        export_data = []
        
        for answer in answers:
            row = {
                '题号': answer.get('question_no', ''),
                '标准答案': answer.get('content', ''),
                '分值': answer.get('score', 1.0),
                '评分类型': answer.get('grading_type', 'exact'),
                '评分规则': answer.get('grading_rule', ''),
                '关键词': ', '.join(answer.get('keywords', [])),
                '创建时间': answer.get('created_at', '')
            }
            export_data.append(row)
        
        return self.export_to_csv(export_data)
    
    def import_standard_answers(self, csv_file) -> List[Dict]:
        """
        从CSV导入标准答案
        
        Args:
            csv_file: CSV文件对象
            
        Returns:
            标准答案列表
        """
        try:
            df = pd.read_csv(csv_file)
            answers = []
            
            for _, row in df.iterrows():
                keywords = []
                if pd.notna(row.get('关键词', '')):
                    keywords = [kw.strip() for kw in str(row['关键词']).split(',') if kw.strip()]
                
                answer = {
                    'question_no': int(row.get('题号', 1)),
                    'content': str(row.get('标准答案', '')),
                    'score': float(row.get('分值', 1.0)),
                    'grading_type': str(row.get('评分类型', 'exact')),
                    'grading_rule': str(row.get('评分规则', '')),
                    'keywords': keywords
                }
                answers.append(answer)
            
            return answers
            
        except Exception as e:
            st.error(f"导入标准答案失败: {e}")
            return []

# 全局导出器实例
data_exporter = DataExporter()
