import re
from difflib import SequenceMatcher
from typing import List, Dict, Tuple
import jieba
import jieba.analyse

class ScoringEngine:
    """评分引擎"""
    
    def __init__(self):
        # 初始化jieba分词
        jieba.initialize()
    
    def score_answer(self, student_answer: str, standard_answer: Dict) -> Dict:
        """
        对学生答案进行评分
        
        Args:
            student_answer: 学生答案
            standard_answer: 标准答案字典，包含content, keywords, grading_type等
            
        Returns:
            评分结果字典
        """
        grading_type = standard_answer.get('grading_type', 'exact')
        total_score = standard_answer.get('score', 1.0)
        
        if grading_type == 'exact':
            return self._exact_match(student_answer, standard_answer, total_score)
        elif grading_type == 'keyword':
            return self._keyword_match(student_answer, standard_answer, total_score)
        elif grading_type == 'fuzzy':
            return self._fuzzy_match(student_answer, standard_answer, total_score)
        elif grading_type == 'custom':
            return self._custom_match(student_answer, standard_answer, total_score)
        else:
            return self._exact_match(student_answer, standard_answer, total_score)
    
    def _exact_match(self, student_answer: str, standard_answer: Dict, total_score: float) -> Dict:
        """完全匹配评分"""
        student_clean = self._clean_text(student_answer)
        standard_clean = self._clean_text(standard_answer['content'])
        
        is_correct = student_clean == standard_clean
        score = total_score if is_correct else 0.0
        
        return {
            'score_earned': score,
            'score_total': total_score,
            'is_correct': is_correct,
            'grading_reason': '完全匹配' if is_correct else '答案不完全匹配',
            'grading_type': 'exact'
        }
    
    def _keyword_match(self, student_answer: str, standard_answer: Dict, total_score: float) -> Dict:
        """关键词匹配评分"""
        keywords = standard_answer.get('keywords', [])
        if not keywords:
            # 如果没有预设关键词，从标准答案中提取
            keywords = self._extract_keywords(standard_answer['content'])
        
        if not keywords:
            return self._exact_match(student_answer, standard_answer, total_score)
        
        student_clean = self._clean_text(student_answer)
        matched_keywords = []
        
        for keyword in keywords:
            if keyword in student_clean:
                matched_keywords.append(keyword)
        
        # 计算匹配比例
        match_ratio = len(matched_keywords) / len(keywords)
        score = total_score * match_ratio
        is_correct = match_ratio >= 0.6  # 60%关键词匹配认为正确
        
        reason = f"关键词匹配 {len(matched_keywords)}/{len(keywords)} ({match_ratio:.1%})"
        if matched_keywords:
            reason += f"，匹配词：{', '.join(matched_keywords)}"
        
        return {
            'score_earned': round(score, 2),
            'score_total': total_score,
            'is_correct': is_correct,
            'grading_reason': reason,
            'grading_type': 'keyword',
            'matched_keywords': matched_keywords
        }
    
    def _fuzzy_match(self, student_answer: str, standard_answer: Dict, total_score: float) -> Dict:
        """模糊匹配评分"""
        student_clean = self._clean_text(student_answer)
        standard_clean = self._clean_text(standard_answer['content'])
        
        # 计算相似度
        similarity = SequenceMatcher(None, student_clean, standard_clean).ratio()
        
        # 基于相似度计算分数
        if similarity >= 0.9:
            score = total_score
            is_correct = True
            reason = f"高度相似 ({similarity:.1%})"
        elif similarity >= 0.7:
            score = total_score * 0.8
            is_correct = True
            reason = f"较为相似 ({similarity:.1%})"
        elif similarity >= 0.5:
            score = total_score * 0.5
            is_correct = False
            reason = f"部分相似 ({similarity:.1%})"
        else:
            score = 0.0
            is_correct = False
            reason = f"相似度较低 ({similarity:.1%})"
        
        return {
            'score_earned': round(score, 2),
            'score_total': total_score,
            'is_correct': is_correct,
            'grading_reason': reason,
            'grading_type': 'fuzzy',
            'similarity': similarity
        }
    
    def _custom_match(self, student_answer: str, standard_answer: Dict, total_score: float) -> Dict:
        """自定义规则匹配"""
        custom_rule = standard_answer.get('grading_rule', '')
        
        if not custom_rule:
            return self._fuzzy_match(student_answer, standard_answer, total_score)
        
        student_clean = self._clean_text(student_answer)
        
        try:
            # 解析自定义规则（简单的包含判断）
            if '包含' in custom_rule:
                # 提取包含的关键词
                keywords = re.findall(r'包含[：:]\s*(.+)', custom_rule)
                if keywords:
                    required_words = [w.strip() for w in keywords[0].split('、')]
                    matched = sum(1 for word in required_words if word in student_clean)
                    match_ratio = matched / len(required_words)
                    score = total_score * match_ratio
                    is_correct = match_ratio >= 0.8
                    
                    return {
                        'score_earned': round(score, 2),
                        'score_total': total_score,
                        'is_correct': is_correct,
                        'grading_reason': f"自定义规则匹配 {matched}/{len(required_words)}",
                        'grading_type': 'custom'
                    }
            
            # 其他自定义规则可以在这里扩展
            
        except Exception as e:
            print(f"自定义规则解析失败: {e}")
        
        # 如果自定义规则解析失败，回退到模糊匹配
        return self._fuzzy_match(student_answer, standard_answer, total_score)
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        if not text:
            return ""
        
        # 移除多余空格和标点符号
        text = re.sub(r'\s+', ' ', text.strip())
        text = re.sub(r'[，。！？；：""''（）【】\[\](),.!?;:"\']+', '', text)
        
        return text.lower()
    
    def _extract_keywords(self, text: str, top_k: int = 5) -> List[str]:
        """从文本中提取关键词"""
        try:
            # 使用jieba提取关键词
            keywords = jieba.analyse.extract_tags(text, topK=top_k, withWeight=False)
            return keywords
        except:
            # 如果jieba失败，使用简单方法
            words = self._clean_text(text).split()
            # 过滤常见停用词
            stop_words = {'的', '是', '在', '有', '和', '与', '或', '但', '因为', '所以', '如果', '那么', '这', '那', '了', '着', '过'}
            keywords = [word for word in words if len(word) > 1 and word not in stop_words]
            return keywords[:top_k]
    
    def batch_score(self, student_answers: List[str], standard_answers: List[Dict]) -> List[Dict]:
        """批量评分"""
        results = []
        
        for i, student_answer in enumerate(student_answers):
            if i < len(standard_answers):
                standard = standard_answers[i]
                result = self.score_answer(student_answer, standard)
                result['question_no'] = standard.get('question_no', i + 1)
                result['student_answer'] = student_answer
                result['standard_answer'] = standard.get('content', '')
                results.append(result)
        
        return results

# 全局评分引擎实例
scoring_engine = ScoringEngine()
